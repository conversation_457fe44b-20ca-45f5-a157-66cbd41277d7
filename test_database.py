#!/usr/bin/env python3
"""
Test script to verify the database setup and functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def test_database():
    """Test database functionality"""
    print("🧪 Testing Laboratory Equipment Committee Management System Database")
    print("=" * 70)
    
    try:
        # Initialize database manager
        print("📦 Initializing database manager...")
        db_manager = DatabaseManager()
        print("✅ Database manager initialized successfully!")
        
        # Test user authentication
        print("\n🔐 Testing user authentication...")
        user = db_manager.authenticate_user("chair", "chair")
        if user:
            print(f"✅ Authentication successful for user: {user['full_name']} ({user['role']})")
        else:
            print("❌ Authentication failed")
            return False
        
        # Test tasks summary
        print("\n📋 Testing tasks summary...")
        tasks_df = db_manager.get_tasks_summary()
        print(f"✅ Retrieved {len(tasks_df)} tasks from database")
        
        if not tasks_df.empty:
            print("📝 Sample tasks:")
            for i, (_, task) in enumerate(tasks_df.head(3).iterrows()):
                print(f"   {i+1}. Task {task['task_number']}: {task['title']}")
                print(f"      Deadline: {task['deadline']}, Budget: SAR {task['budget_allocation']:,.0f}")
        
        # Test laboratories summary
        print("\n🔬 Testing laboratories summary...")
        labs_df = db_manager.get_labs_summary()
        print(f"✅ Retrieved {len(labs_df)} laboratories from database")
        
        if not labs_df.empty:
            print("🏢 Sample laboratories:")
            for i, (_, lab) in enumerate(labs_df.head(3).iterrows()):
                print(f"   {i+1}. {lab['lab_name']} ({lab['lab_code']})")
                print(f"      Location: {lab['location']}, Status: {lab['status']}")
        
        # Test suppliers summary
        print("\n🚚 Testing suppliers summary...")
        suppliers_df = db_manager.get_suppliers_summary()
        print(f"✅ Retrieved {len(suppliers_df)} suppliers from database")
        
        if not suppliers_df.empty:
            print("🏭 Sample suppliers:")
            for i, (_, supplier) in enumerate(suppliers_df.head(3).iterrows()):
                print(f"   {i+1}. {supplier['supplier_name']}")
                print(f"      Specialization: {supplier['specialization']}, Rating: {supplier['rating']}/5.0")
        
        # Test KPI data
        print("\n📊 Testing KPI data...")
        kpi_data = db_manager.get_kpi_data()
        print("✅ KPI data retrieved successfully!")
        
        task_stats = kpi_data.get('task_stats', {})
        lab_stats = kpi_data.get('lab_stats', {})
        budget_data = kpi_data.get('budget_data', {})
        
        print("📈 KPI Summary:")
        print(f"   • Task Statistics: {task_stats}")
        print(f"   • Lab Statistics: {lab_stats}")
        print(f"   • Budget Data: Total SAR {budget_data.get('total_budget', 0):,.0f}")
        
        print("\n" + "=" * 70)
        print("🎉 All database tests passed successfully!")
        print("🌐 The application is ready to use at: http://localhost:8501")
        print("\n📋 Sample Login Credentials:")
        print("   • Committee Chair: chair / chair")
        print("   • Committee Member: member1 / member1")
        print("   • Lab Supervisor: supervisor1 / supervisor1")
        print("   • Faculty: faculty1 / faculty1")
        print("   • Admin: admin / admin")
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_database()
    sys.exit(0 if success else 1)
