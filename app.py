import streamlit as st
from streamlit_option_menu import option_menu
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.auth import init_session_state, require_auth, logout, get_user_info
from components.dashboard_components import (
    display_kpi_cards, create_task_progress_chart, create_timeline_chart,
    display_recent_activities, display_upcoming_deadlines,
    display_lab_status_overview
)
from pages.task_management import show_task_management
from pages.laboratory_management import show_laboratory_management
from pages.supplier_management import show_supplier_management

# Page configuration
st.set_page_config(
    page_title="نظام إدارة لجنة معدات المختبرات",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS with RTL support
st.markdown("""
<style>
    /* RTL Support */
    .main .block-container {
        direction: rtl;
        text-align: right;
    }

    .main-header {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
        direction: rtl;
    }

    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-right: 4px solid #2a5298;
        direction: rtl;
    }

    .sidebar .sidebar-content {
        background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
        direction: rtl;
    }

    .stMetric {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        direction: rtl;
        text-align: right;
    }

    /* Arabic font support */
    * {
        font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    }

    /* Sidebar RTL */
    .css-1d391kg {
        direction: rtl;
    }

    /* Navigation menu RTL */
    .nav-link {
        text-align: right !important;
        direction: rtl !important;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Initialize session state
    init_session_state()
    
    # Check authentication
    if not require_auth():
        return
    
    # Get user info
    user = get_user_info()
    
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🏥 نظام إدارة لجنة معدات المختبرات</h1>
        <h3>قسم الأشعة التشخيصية - كلية العلوم الطبية التطبيقية</h3>
    </div>
    """, unsafe_allow_html=True)
    
    # Sidebar navigation
    with st.sidebar:
        st.markdown(f"### أهلاً وسهلاً، {user['full_name']}")
        st.markdown(f"**الدور:** {user['role']}")
        st.markdown(f"**القسم:** {user['department']}")
        st.divider()

        # Navigation menu
        selected = option_menu(
            menu_title="التنقل",
            options=[
                "لوحة التحكم",
                "إدارة المهام",
                "إدارة المختبرات",
                "إدارة الموردين",
                "السلامة والامتثال",
                "التكامل الأكاديمي",
                "المشتريات",
                "التقارير",
                "الإعدادات"
            ],
            icons=[
                "speedometer2",
                "list-task",
                "building",
                "truck",
                "shield-check",
                "book",
                "cart",
                "graph-up",
                "gear"
            ],
            menu_icon="list",
            default_index=0,
            styles={
                "container": {"padding": "0!important", "background-color": "#fafafa"},
                "icon": {"color": "#2a5298", "font-size": "18px"},
                "nav-link": {
                    "font-size": "16px",
                    "text-align": "right",
                    "margin": "0px",
                    "--hover-color": "#eee",
                    "direction": "rtl"
                },
                "nav-link-selected": {"background-color": "#2a5298"},
            }
        )

        st.divider()
        if st.button("🚪 تسجيل الخروج", use_container_width=True):
            logout()
    
    # Main content area
    if selected == "لوحة التحكم":
        show_dashboard()
    elif selected == "إدارة المهام":
        show_task_management()
    elif selected == "إدارة المختبرات":
        show_laboratory_management()
    elif selected == "إدارة الموردين":
        show_supplier_management()
    elif selected == "السلامة والامتثال":
        show_safety_compliance()
    elif selected == "التكامل الأكاديمي":
        show_academic_integration()
    elif selected == "المشتريات":
        show_procurement()
    elif selected == "التقارير":
        show_reports()
    elif selected == "الإعدادات":
        show_settings()

def show_dashboard():
    """Main dashboard page"""
    st.title("📊 نظرة عامة على لوحة التحكم")

    # Get data from database
    db_manager = st.session_state.db_manager
    kpi_data = db_manager.get_kpi_data()
    tasks_df = db_manager.get_tasks_summary()
    labs_df = db_manager.get_labs_summary()

    # KPI Cards
    display_kpi_cards(kpi_data)

    st.divider()

    # Main dashboard content
    col1, col2 = st.columns([2, 1])

    with col1:
        # Task progress chart
        st.subheader("📋 نظرة عامة على تقدم المهام")
        create_task_progress_chart(tasks_df)

        # Timeline chart
        st.subheader("📅 الجدول الزمني للمشروع")
        create_timeline_chart(tasks_df)

    with col2:
        # Recent activities
        display_recent_activities()

        # Upcoming deadlines
        display_upcoming_deadlines(tasks_df)

        # Lab status overview
        display_lab_status_overview(labs_df)

# These functions are now imported from separate page modules

def show_safety_compliance():
    """Safety and compliance page"""
    st.title("🛡️ السلامة والامتثال")
    st.info("سيتم تنفيذ وظائف إدارة السلامة والامتثال هنا.")

def show_academic_integration():
    """Academic integration page"""
    st.title("📚 التكامل الأكاديمي")
    st.info("سيتم تنفيذ وظائف التكامل الأكاديمي هنا.")

def show_procurement():
    """Procurement management page"""
    st.title("🛒 إدارة المشتريات")
    st.info("سيتم تنفيذ وظائف إدارة المشتريات هنا.")

def show_reports():
    """Reports page"""
    st.title("📊 التقارير والتحليلات")
    st.info("سيتم تنفيذ وظائف التقارير والتحليلات هنا.")

def show_settings():
    """Settings page"""
    st.title("⚙️ الإعدادات")
    st.info("سيتم تنفيذ وظائف إعدادات النظام هنا.")

if __name__ == "__main__":
    main()
