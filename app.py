import streamlit as st
from streamlit_option_menu import option_menu
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.auth import init_session_state, require_auth, logout, get_user_info
from components.dashboard_components import (
    display_kpi_cards, create_task_progress_chart, create_timeline_chart,
    display_recent_activities, display_upcoming_deadlines,
    display_lab_status_overview
)
from pages.task_management import show_task_management
from pages.laboratory_management import show_laboratory_management
from pages.supplier_management import show_supplier_management

# Page configuration
st.set_page_config(
    page_title="نظام إدارة لجنة معدات المختبرات",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS with comprehensive RTL support
st.markdown("""
<style>
    @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap');

    /* Global RTL Support */
    html, body, [data-testid="stAppViewContainer"], .main, .block-container {
        direction: rtl !important;
        text-align: right !important;
        font-family: 'Noto Sans Arabic', 'Segoe UI', 'Tahoma', 'Arial', sans-serif !important;
    }

    /* Sidebar positioning - move to right */
    .css-1d391kg, [data-testid="stSidebar"] {
        position: fixed !important;
        right: 0 !important;
        left: auto !important;
        direction: rtl !important;
        text-align: right !important;
    }

    /* Main content adjustment for right sidebar */
    .main .block-container {
        margin-right: 21rem !important;
        margin-left: 1rem !important;
        direction: rtl !important;
        text-align: right !important;
        padding-right: 1rem !important;
        padding-left: 1rem !important;
    }

    /* Header styling */
    .main-header {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        padding: 1.5rem;
        border-radius: 10px;
        color: white !important;
        text-align: center;
        margin-bottom: 2rem;
        direction: rtl;
        font-weight: 600;
    }

    .main-header h1, .main-header h3 {
        color: white !important;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    /* Metric cards */
    .stMetric {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        direction: rtl !important;
        text-align: right !important;
        border-right: 4px solid #2a5298;
        border-left: none;
    }

    .stMetric > div {
        direction: rtl !important;
        text-align: right !important;
    }

    .stMetric label {
        color: #2a5298 !important;
        font-weight: 600 !important;
        font-size: 0.9rem !important;
    }

    .stMetric [data-testid="metric-container"] > div {
        color: #1e3c72 !important;
        font-weight: 700 !important;
        font-size: 1.5rem !important;
    }

    /* Sidebar styling */
    .css-1d391kg .css-1v0mbdj {
        direction: rtl !important;
        text-align: right !important;
    }

    /* Text inputs and forms */
    .stTextInput > div > div > input {
        direction: rtl !important;
        text-align: right !important;
        font-family: 'Noto Sans Arabic', sans-serif !important;
    }

    .stSelectbox > div > div > div {
        direction: rtl !important;
        text-align: right !important;
    }

    /* Buttons */
    .stButton > button {
        direction: rtl !important;
        font-family: 'Noto Sans Arabic', sans-serif !important;
        font-weight: 500 !important;
    }

    /* Tables and dataframes */
    .stDataFrame {
        direction: rtl !important;
    }

    .stDataFrame table {
        direction: rtl !important;
    }

    .stDataFrame th, .stDataFrame td {
        text-align: right !important;
        direction: rtl !important;
    }

    /* Tabs */
    .stTabs [data-baseweb="tab-list"] {
        direction: rtl !important;
        justify-content: flex-end !important;
    }

    .stTabs [data-baseweb="tab"] {
        direction: rtl !important;
        text-align: right !important;
    }

    /* Expanders */
    .streamlit-expanderHeader {
        direction: rtl !important;
        text-align: right !important;
        font-family: 'Noto Sans Arabic', sans-serif !important;
    }

    .streamlit-expanderContent {
        direction: rtl !important;
        text-align: right !important;
    }

    /* Columns */
    .element-container {
        direction: rtl !important;
    }

    /* Text elements */
    .stMarkdown, .stText, .stCaption {
        direction: rtl !important;
        text-align: right !important;
        color: #2c3e50 !important;
    }

    /* Titles and headers */
    h1, h2, h3, h4, h5, h6 {
        direction: rtl !important;
        text-align: right !important;
        color: #1e3c72 !important;
        font-family: 'Noto Sans Arabic', sans-serif !important;
        font-weight: 600 !important;
    }

    /* Info boxes */
    .stInfo {
        direction: rtl !important;
        text-align: right !important;
    }

    .stSuccess {
        direction: rtl !important;
        text-align: right !important;
    }

    .stError {
        direction: rtl !important;
        text-align: right !important;
    }

    /* Navigation menu specific */
    .nav-link {
        text-align: right !important;
        direction: rtl !important;
        font-family: 'Noto Sans Arabic', sans-serif !important;
    }

    /* Plotly charts RTL */
    .js-plotly-plot {
        direction: ltr !important;
    }

    /* Form labels */
    label {
        direction: rtl !important;
        text-align: right !important;
        color: #2c3e50 !important;
        font-weight: 500 !important;
    }

    /* File uploader */
    .stFileUploader {
        direction: rtl !important;
        text-align: right !important;
    }

    /* Text areas */
    .stTextArea textarea {
        direction: rtl !important;
        text-align: right !important;
        font-family: 'Noto Sans Arabic', sans-serif !important;
    }

    /* Dividers */
    .stDivider {
        margin: 1rem 0 !important;
    }

    /* Improve contrast for better readability */
    p, span, div {
        color: #2c3e50 !important;
    }

    /* Sidebar text color */
    .css-1d391kg p, .css-1d391kg span, .css-1d391kg div {
        color: #2c3e50 !important;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Initialize session state
    init_session_state()
    
    # Check authentication
    if not require_auth():
        return
    
    # Get user info
    user = get_user_info()
    
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🏥 نظام إدارة لجنة معدات المختبرات</h1>
        <h3>قسم الأشعة التشخيصية - كلية العلوم الطبية التطبيقية</h3>
    </div>
    """, unsafe_allow_html=True)
    
    # Sidebar navigation
    with st.sidebar:
        st.markdown(f"### أهلاً وسهلاً، {user['full_name']}")
        st.markdown(f"**الدور:** {user['role']}")
        st.markdown(f"**القسم:** {user['department']}")
        st.divider()

        # Navigation menu
        selected = option_menu(
            menu_title="التنقل",
            options=[
                "لوحة التحكم",
                "إدارة المهام",
                "إدارة المختبرات",
                "إدارة الموردين",
                "السلامة والامتثال",
                "التكامل الأكاديمي",
                "المشتريات",
                "التقارير",
                "الإعدادات"
            ],
            icons=[
                "speedometer2",
                "list-task",
                "building",
                "truck",
                "shield-check",
                "book",
                "cart",
                "graph-up",
                "gear"
            ],
            menu_icon="list",
            default_index=0,
            styles={
                "container": {"padding": "0!important", "background-color": "#fafafa"},
                "icon": {"color": "#2a5298", "font-size": "18px"},
                "nav-link": {
                    "font-size": "16px",
                    "text-align": "right",
                    "margin": "0px",
                    "--hover-color": "#eee",
                    "direction": "rtl"
                },
                "nav-link-selected": {"background-color": "#2a5298"},
            }
        )

        st.divider()
        if st.button("🚪 تسجيل الخروج", use_container_width=True):
            logout()
    
    # Main content area
    if selected == "لوحة التحكم":
        show_dashboard()
    elif selected == "إدارة المهام":
        show_task_management()
    elif selected == "إدارة المختبرات":
        show_laboratory_management()
    elif selected == "إدارة الموردين":
        show_supplier_management()
    elif selected == "السلامة والامتثال":
        show_safety_compliance()
    elif selected == "التكامل الأكاديمي":
        show_academic_integration()
    elif selected == "المشتريات":
        show_procurement()
    elif selected == "التقارير":
        show_reports()
    elif selected == "الإعدادات":
        show_settings()

def show_dashboard():
    """Main dashboard page"""
    st.title("📊 نظرة عامة على لوحة التحكم")

    # Get data from database
    db_manager = st.session_state.db_manager
    kpi_data = db_manager.get_kpi_data()
    tasks_df = db_manager.get_tasks_summary()
    labs_df = db_manager.get_labs_summary()

    # KPI Cards
    display_kpi_cards(kpi_data)

    st.divider()

    # Main dashboard content
    col1, col2 = st.columns([2, 1])

    with col1:
        # Task progress chart
        st.subheader("📋 نظرة عامة على تقدم المهام")
        create_task_progress_chart(tasks_df)

        # Timeline chart
        st.subheader("📅 الجدول الزمني للمشروع")
        create_timeline_chart(tasks_df)

    with col2:
        # Recent activities
        display_recent_activities()

        # Upcoming deadlines
        display_upcoming_deadlines(tasks_df)

        # Lab status overview
        display_lab_status_overview(labs_df)

# These functions are now imported from separate page modules

def show_safety_compliance():
    """Safety and compliance page"""
    st.title("🛡️ السلامة والامتثال")
    st.info("سيتم تنفيذ وظائف إدارة السلامة والامتثال هنا.")

def show_academic_integration():
    """Academic integration page"""
    st.title("📚 التكامل الأكاديمي")
    st.info("سيتم تنفيذ وظائف التكامل الأكاديمي هنا.")

def show_procurement():
    """Procurement management page"""
    st.title("🛒 إدارة المشتريات")
    st.info("سيتم تنفيذ وظائف إدارة المشتريات هنا.")

def show_reports():
    """Reports page"""
    st.title("📊 التقارير والتحليلات")
    st.info("سيتم تنفيذ وظائف التقارير والتحليلات هنا.")

def show_settings():
    """Settings page"""
    st.title("⚙️ الإعدادات")
    st.info("سيتم تنفيذ وظائف إعدادات النظام هنا.")

if __name__ == "__main__":
    main()
