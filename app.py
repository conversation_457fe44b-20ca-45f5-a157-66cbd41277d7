import streamlit as st
from streamlit_option_menu import option_menu
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.auth import init_session_state, require_auth, logout, get_user_info
from components.dashboard_components import (
    display_kpi_cards, create_task_progress_chart, create_timeline_chart,
    create_budget_chart, display_recent_activities, display_upcoming_deadlines,
    display_lab_status_overview
)
from pages.task_management import show_task_management
from pages.laboratory_management import show_laboratory_management
from pages.supplier_management import show_supplier_management

# Page configuration
st.set_page_config(
    page_title="Laboratory Equipment Committee Management System",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #2a5298;
    }
    
    .sidebar .sidebar-content {
        background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
    }
    
    .stMetric {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Initialize session state
    init_session_state()
    
    # Check authentication
    if not require_auth():
        return
    
    # Get user info
    user = get_user_info()
    
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🏥 Laboratory Equipment Committee Management System</h1>
        <h3>Department of Diagnostic Radiology - College of Applied Medical Sciences</h3>
    </div>
    """, unsafe_allow_html=True)
    
    # Sidebar navigation
    with st.sidebar:
        st.markdown(f"### Welcome, {user['full_name']}")
        st.markdown(f"**Role:** {user['role']}")
        st.markdown(f"**Department:** {user['department']}")
        st.divider()
        
        # Navigation menu
        selected = option_menu(
            menu_title="Navigation",
            options=[
                "Dashboard",
                "Task Management", 
                "Laboratory Management",
                "Supplier Management",
                "Safety & Compliance",
                "Academic Integration",
                "Procurement",
                "Reports",
                "Settings"
            ],
            icons=[
                "speedometer2",
                "list-task",
                "building",
                "truck",
                "shield-check",
                "book",
                "cart",
                "graph-up",
                "gear"
            ],
            menu_icon="list",
            default_index=0,
            styles={
                "container": {"padding": "0!important", "background-color": "#fafafa"},
                "icon": {"color": "#2a5298", "font-size": "18px"},
                "nav-link": {
                    "font-size": "16px",
                    "text-align": "left",
                    "margin": "0px",
                    "--hover-color": "#eee"
                },
                "nav-link-selected": {"background-color": "#2a5298"},
            }
        )
        
        st.divider()
        if st.button("🚪 Logout", use_container_width=True):
            logout()
    
    # Main content area
    if selected == "Dashboard":
        show_dashboard()
    elif selected == "Task Management":
        show_task_management()
    elif selected == "Laboratory Management":
        show_laboratory_management()
    elif selected == "Supplier Management":
        show_supplier_management()
    elif selected == "Safety & Compliance":
        show_safety_compliance()
    elif selected == "Academic Integration":
        show_academic_integration()
    elif selected == "Procurement":
        show_procurement()
    elif selected == "Reports":
        show_reports()
    elif selected == "Settings":
        show_settings()

def show_dashboard():
    """Main dashboard page"""
    st.title("📊 Dashboard Overview")
    
    # Get data from database
    db_manager = st.session_state.db_manager
    kpi_data = db_manager.get_kpi_data()
    tasks_df = db_manager.get_tasks_summary()
    labs_df = db_manager.get_labs_summary()
    
    # KPI Cards
    display_kpi_cards(kpi_data)
    
    st.divider()
    
    # Main dashboard content
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Task progress chart
        st.subheader("📋 Task Progress Overview")
        create_task_progress_chart(tasks_df)
        
        # Timeline chart
        st.subheader("📅 Project Timeline")
        create_timeline_chart(tasks_df)
        
        # Budget allocation
        st.subheader("💰 Budget Allocation")
        create_budget_chart(tasks_df)
    
    with col2:
        # Recent activities
        display_recent_activities()
        
        # Upcoming deadlines
        display_upcoming_deadlines(tasks_df)
        
        # Lab status overview
        display_lab_status_overview(labs_df)

# These functions are now imported from separate page modules

def show_safety_compliance():
    """Safety and compliance page"""
    st.title("🛡️ Safety & Compliance")
    st.info("Safety and compliance management functionality will be implemented here.")

def show_academic_integration():
    """Academic integration page"""
    st.title("📚 Academic Integration")
    st.info("Academic integration functionality will be implemented here.")

def show_procurement():
    """Procurement management page"""
    st.title("🛒 Procurement Management")
    st.info("Procurement management functionality will be implemented here.")

def show_reports():
    """Reports page"""
    st.title("📊 Reports & Analytics")
    st.info("Reports and analytics functionality will be implemented here.")

def show_settings():
    """Settings page"""
    st.title("⚙️ Settings")
    st.info("System settings functionality will be implemented here.")

if __name__ == "__main__":
    main()
