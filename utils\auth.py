import streamlit as st
from database.db_manager import DatabaseManager

def init_session_state():
    """Initialize session state variables"""
    if 'authenticated' not in st.session_state:
        st.session_state.authenticated = False
    if 'user' not in st.session_state:
        st.session_state.user = None
    if 'db_manager' not in st.session_state:
        st.session_state.db_manager = DatabaseManager()

def login_form():
    """Display login form"""
    st.title("🏥 نظام إدارة لجنة معدات المختبرات")
    st.markdown("### قسم الأشعة التشخيصية")
    st.markdown("**كلية العلوم الطبية التطبيقية**")

    with st.form("login_form"):
        st.subheader("تسجيل الدخول")
        username = st.text_input("اسم المستخدم")
        password = st.text_input("كلمة المرور", type="password")
        submit_button = st.form_submit_button("دخول")

        if submit_button:
            if username and password:
                user = st.session_state.db_manager.authenticate_user(username, password)
                if user:
                    st.session_state.authenticated = True
                    st.session_state.user = user
                    st.success(f"أهلاً وسهلاً، {user['full_name']}!")
                    st.rerun()
                else:
                    st.error("اسم المستخدم أو كلمة المرور غير صحيحة")
            else:
                st.error("يرجى إدخال اسم المستخدم وكلمة المرور")
    
    # Display sample credentials
    with st.expander("بيانات تسجيل الدخول التجريبية"):
        st.markdown("""
        **رئيس اللجنة:**
        - اسم المستخدم: `chair`
        - كلمة المرور: `chair`

        **عضو اللجنة:**
        - اسم المستخدم: `member1`
        - كلمة المرور: `member1`

        **مشرف المختبر:**
        - اسم المستخدم: `supervisor1`
        - كلمة المرور: `supervisor1`

        **عضو هيئة التدريس:**
        - اسم المستخدم: `faculty1`
        - كلمة المرور: `faculty1`

        **المدير:**
        - اسم المستخدم: `admin`
        - كلمة المرور: `admin`
        """)

def logout():
    """Logout user"""
    st.session_state.authenticated = False
    st.session_state.user = None
    st.rerun()

def require_auth():
    """Require authentication for page access"""
    if not st.session_state.authenticated:
        login_form()
        return False
    return True

def has_role(required_roles):
    """Check if user has required role"""
    if not st.session_state.authenticated:
        return False
    
    user_role = st.session_state.user['role']
    if isinstance(required_roles, str):
        required_roles = [required_roles]
    
    return user_role in required_roles or user_role == 'Admin'

def get_user_info():
    """Get current user information"""
    return st.session_state.user if st.session_state.authenticated else None
