import streamlit as st
from database.db_manager import DatabaseManager

def init_session_state():
    """Initialize session state variables"""
    if 'authenticated' not in st.session_state:
        st.session_state.authenticated = False
    if 'user' not in st.session_state:
        st.session_state.user = None
    if 'db_manager' not in st.session_state:
        st.session_state.db_manager = DatabaseManager()

def login_form():
    """Display login form"""
    st.title("🏥 Laboratory Equipment Committee Management System")
    st.markdown("### Department of Diagnostic Radiology")
    st.markdown("**College of Applied Medical Sciences**")
    
    with st.form("login_form"):
        st.subheader("Login")
        username = st.text_input("Username")
        password = st.text_input("Password", type="password")
        submit_button = st.form_submit_button("Login")
        
        if submit_button:
            if username and password:
                user = st.session_state.db_manager.authenticate_user(username, password)
                if user:
                    st.session_state.authenticated = True
                    st.session_state.user = user
                    st.success(f"Welcome, {user['full_name']}!")
                    st.rerun()
                else:
                    st.error("Invalid username or password")
            else:
                st.error("Please enter both username and password")
    
    # Display sample credentials
    with st.expander("Sample Login Credentials"):
        st.markdown("""
        **Committee Chair:**
        - Username: `chair`
        - Password: `chair`
        
        **Committee Member:**
        - Username: `member1`
        - Password: `member1`
        
        **Lab Supervisor:**
        - Username: `supervisor1`
        - Password: `supervisor1`
        
        **Faculty:**
        - Username: `faculty1`
        - Password: `faculty1`
        
        **Admin:**
        - Username: `admin`
        - Password: `admin`
        """)

def logout():
    """Logout user"""
    st.session_state.authenticated = False
    st.session_state.user = None
    st.rerun()

def require_auth():
    """Require authentication for page access"""
    if not st.session_state.authenticated:
        login_form()
        return False
    return True

def has_role(required_roles):
    """Check if user has required role"""
    if not st.session_state.authenticated:
        return False
    
    user_role = st.session_state.user['role']
    if isinstance(required_roles, str):
        required_roles = [required_roles]
    
    return user_role in required_roles or user_role == 'Admin'

def get_user_info():
    """Get current user information"""
    return st.session_state.user if st.session_state.authenticated else None
