import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import pandas as pd

def display_kpi_cards(kpi_data):
    """Display KPI cards on dashboard"""
    col1, col2, col3, col4 = st.columns(4)
    
    # Task completion metrics
    task_stats = kpi_data.get('task_stats', {})
    total_tasks = sum(task_stats.values())
    completed_tasks = task_stats.get('Completed', 0)
    in_progress_tasks = task_stats.get('In Progress', 0)
    
    with col1:
        st.metric(
            label="📋 Total Tasks",
            value=total_tasks,
            delta=f"{completed_tasks} completed"
        )
    
    with col2:
        completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        st.metric(
            label="✅ Completion Rate",
            value=f"{completion_rate:.1f}%",
            delta=f"{in_progress_tasks} in progress"
        )
    
    # Lab operational metrics
    lab_stats = kpi_data.get('lab_stats', {})
    total_labs = sum(lab_stats.values())
    operational_labs = lab_stats.get('Operational', 0)
    
    with col3:
        st.metric(
            label="🔬 Operational Labs",
            value=f"{operational_labs}/{total_labs}",
            delta=f"{(operational_labs/total_labs*100):.1f}% operational" if total_labs > 0 else "0% operational"
        )
    
    # Budget metrics
    budget_data = kpi_data.get('budget_data', {})
    total_budget = budget_data.get('total_budget', 0) or 0
    used_budget = budget_data.get('used_budget', 0) or 0
    
    with col4:
        budget_utilization = (used_budget / total_budget * 100) if total_budget > 0 else 0
        st.metric(
            label="💰 Budget Utilization",
            value=f"{budget_utilization:.1f}%",
            delta=f"SAR {used_budget:,.0f} / {total_budget:,.0f}"
        )

def create_task_progress_chart(tasks_df):
    """Create task progress visualization"""
    if tasks_df.empty:
        st.info("No task data available")
        return
    
    # Task status distribution
    status_counts = tasks_df['status'].value_counts()
    
    fig = px.pie(
        values=status_counts.values,
        names=status_counts.index,
        title="Task Status Distribution",
        color_discrete_map={
            'Not Started': '#ff7f7f',
            'In Progress': '#ffb347',
            'Completed': '#90ee90',
            'On Hold': '#d3d3d3'
        }
    )
    
    fig.update_traces(textposition='inside', textinfo='percent+label')
    fig.update_layout(height=400)
    
    st.plotly_chart(fig, use_container_width=True)

def create_timeline_chart(tasks_df):
    """Create project timeline chart"""
    if tasks_df.empty:
        st.info("No task data available")
        return
    
    # Convert deadline to datetime
    tasks_df['deadline'] = pd.to_datetime(tasks_df['deadline'])
    tasks_df = tasks_df.sort_values('deadline')
    
    # Create Gantt-like chart
    fig = go.Figure()
    
    colors = {
        'Not Started': '#ff7f7f',
        'In Progress': '#ffb347', 
        'Completed': '#90ee90',
        'On Hold': '#d3d3d3'
    }
    
    for i, row in tasks_df.iterrows():
        fig.add_trace(go.Scatter(
            x=[row['deadline']],
            y=[f"Task {row['task_number']}: {row['title'][:30]}..."],
            mode='markers',
            marker=dict(
                size=15,
                color=colors.get(row['status'], '#cccccc'),
                symbol='circle'
            ),
            name=row['status'],
            showlegend=False,
            hovertemplate=f"<b>{row['title']}</b><br>" +
                         f"Deadline: {row['deadline'].strftime('%Y-%m-%d')}<br>" +
                         f"Status: {row['status']}<br>" +
                         f"Budget: SAR {row['budget_allocation']:,.0f}<extra></extra>"
        ))
    
    fig.update_layout(
        title="Project Timeline",
        xaxis_title="Deadline",
        yaxis_title="Tasks",
        height=600,
        showlegend=False
    )
    
    st.plotly_chart(fig, use_container_width=True)

def create_budget_chart(tasks_df):
    """Create budget allocation chart"""
    if tasks_df.empty:
        st.info("No task data available")
        return
    
    # Budget by task
    fig = px.bar(
        tasks_df,
        x='task_number',
        y='budget_allocation',
        color='status',
        title="Budget Allocation by Task",
        labels={
            'task_number': 'Task Number',
            'budget_allocation': 'Budget (SAR)',
            'status': 'Status'
        },
        color_discrete_map={
            'Not Started': '#ff7f7f',
            'In Progress': '#ffb347',
            'Completed': '#90ee90',
            'On Hold': '#d3d3d3'
        }
    )
    
    fig.update_layout(height=400)
    st.plotly_chart(fig, use_container_width=True)

def display_recent_activities():
    """Display recent activities widget"""
    st.subheader("📈 Recent Activities")
    
    activities = [
        {"time": "2 hours ago", "activity": "Task 3: Lab assessment completed", "user": "Dr. Sarah Al-Mahmoud"},
        {"time": "1 day ago", "activity": "Equipment maintenance request submitted", "user": "Eng. Fatima Al-Zahra"},
        {"time": "2 days ago", "activity": "Safety audit scheduled for MRI Lab", "user": "Dr. Ahmed Al-Rashid"},
        {"time": "3 days ago", "activity": "Supplier database updated", "user": "Dr. Mohammed Al-Qasim"},
        {"time": "1 week ago", "activity": "Monthly committee meeting held", "user": "Dr. Sarah Al-Mahmoud"}
    ]
    
    for activity in activities:
        with st.container():
            col1, col2 = st.columns([3, 1])
            with col1:
                st.write(f"**{activity['activity']}**")
                st.caption(f"by {activity['user']}")
            with col2:
                st.caption(activity['time'])
            st.divider()

def display_upcoming_deadlines(tasks_df):
    """Display upcoming deadlines"""
    st.subheader("⏰ Upcoming Deadlines")
    
    if tasks_df.empty:
        st.info("No upcoming deadlines")
        return
    
    # Convert deadline to datetime and filter upcoming
    tasks_df['deadline'] = pd.to_datetime(tasks_df['deadline'])
    today = datetime.now()
    upcoming = tasks_df[
        (tasks_df['deadline'] >= today) & 
        (tasks_df['status'] != 'Completed')
    ].sort_values('deadline').head(5)
    
    for _, task in upcoming.iterrows():
        days_left = (task['deadline'] - today).days
        
        if days_left <= 7:
            urgency_color = "🔴"
        elif days_left <= 30:
            urgency_color = "🟡"
        else:
            urgency_color = "🟢"
        
        st.write(f"{urgency_color} **Task {task['task_number']}**: {task['title']}")
        st.caption(f"Due: {task['deadline'].strftime('%Y-%m-%d')} ({days_left} days left)")
        st.divider()

def display_lab_status_overview(labs_df):
    """Display laboratory status overview"""
    st.subheader("🔬 Laboratory Status Overview")
    
    if labs_df.empty:
        st.info("No laboratory data available")
        return
    
    # Status distribution
    status_counts = labs_df['status'].value_counts()
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Status pie chart
        fig = px.pie(
            values=status_counts.values,
            names=status_counts.index,
            title="Lab Status Distribution",
            color_discrete_map={
                'Operational': '#90ee90',
                'Under Maintenance': '#ffb347',
                'Out of Order': '#ff7f7f'
            }
        )
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # Lab details table
        st.dataframe(
            labs_df[['lab_name', 'status', 'capacity']],
            use_container_width=True,
            hide_index=True
        )
