import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import pandas as pd

def display_kpi_cards(kpi_data):
    """Display KPI cards on dashboard"""
    col1, col2, col3 = st.columns(3)

    # Task completion metrics
    task_stats = kpi_data.get('task_stats', {})
    total_tasks = sum(task_stats.values())
    completed_tasks = task_stats.get('مكتملة', 0)
    in_progress_tasks = task_stats.get('قيد التنفيذ', 0)
    not_started_tasks = task_stats.get('لم تبدأ', 0)

    with col1:
        st.metric(
            label="📋 إجمالي المهام",
            value=total_tasks,
            delta=f"{completed_tasks} مكتملة"
        )

    with col2:
        completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        st.metric(
            label="✅ معدل الإنجاز",
            value=f"{completion_rate:.1f}%",
            delta=f"{in_progress_tasks} قيد التنفيذ"
        )

    # Lab operational metrics
    lab_stats = kpi_data.get('lab_stats', {})
    total_labs = sum(lab_stats.values())
    operational_labs = lab_stats.get('تشغيلي', 0)

    with col3:
        operational_rate = (operational_labs/total_labs*100) if total_labs > 0 else 0
        st.metric(
            label="🔬 المختبرات التشغيلية",
            value=f"{operational_labs}/{total_labs}",
            delta=f"{operational_rate:.1f}% تشغيلية"
        )

def create_task_progress_chart(tasks_df):
    """Create task progress visualization"""
    if tasks_df.empty:
        st.info("لا توجد بيانات مهام متاحة")
        return

    # Task status distribution
    status_counts = tasks_df['status'].value_counts()

    fig = px.pie(
        values=status_counts.values,
        names=status_counts.index,
        title="توزيع حالة المهام",
        color_discrete_map={
            'لم تبدأ': '#ff7f7f',
            'قيد التنفيذ': '#ffb347',
            'مكتملة': '#90ee90',
            'معلقة': '#d3d3d3'
        }
    )

    fig.update_traces(textposition='inside', textinfo='percent+label')
    fig.update_layout(height=400, font=dict(family="Arial"))

    st.plotly_chart(fig, use_container_width=True)

def create_timeline_chart(tasks_df):
    """Create project timeline chart"""
    if tasks_df.empty:
        st.info("لا توجد بيانات مهام متاحة")
        return

    # Convert deadline to datetime
    tasks_df['deadline'] = pd.to_datetime(tasks_df['deadline'])
    tasks_df = tasks_df.sort_values('deadline')

    # Create Gantt-like chart
    fig = go.Figure()

    colors = {
        'لم تبدأ': '#ff7f7f',
        'قيد التنفيذ': '#ffb347',
        'مكتملة': '#90ee90',
        'معلقة': '#d3d3d3'
    }

    for i, row in tasks_df.iterrows():
        fig.add_trace(go.Scatter(
            x=[row['deadline']],
            y=[f"مهمة {row['task_number']}: {row['title'][:30]}..."],
            mode='markers',
            marker=dict(
                size=15,
                color=colors.get(row['status'], '#cccccc'),
                symbol='circle'
            ),
            name=row['status'],
            showlegend=False,
            hovertemplate=f"<b>{row['title']}</b><br>" +
                         f"الموعد النهائي: {row['deadline'].strftime('%Y-%m-%d')}<br>" +
                         f"الحالة: {row['status']}<br>" +
                         f"الساعات المقدرة: {row['estimated_hours']}<extra></extra>"
        ))

    fig.update_layout(
        title="الجدول الزمني للمشروع",
        xaxis_title="الموعد النهائي",
        yaxis_title="المهام",
        height=600,
        showlegend=False,
        font=dict(family="Arial")
    )

    st.plotly_chart(fig, use_container_width=True)



def display_recent_activities():
    """Display recent activities widget"""
    st.subheader("📈 الأنشطة الحديثة")

    activities = [
        {"time": "منذ ساعتين", "activity": "المهمة 3: تم إكمال تقييم المختبر", "user": "د. سارة المحمود"},
        {"time": "منذ يوم واحد", "activity": "تم تقديم طلب صيانة المعدات", "user": "م. فاطمة الزهراء"},
        {"time": "منذ يومين", "activity": "تم جدولة تدقيق السلامة لمختبر الرنين المغناطيسي", "user": "د. أحمد الراشد"},
        {"time": "منذ 3 أيام", "activity": "تم تحديث قاعدة بيانات الموردين", "user": "د. محمد القاسم"},
        {"time": "منذ أسبوع", "activity": "تم عقد اجتماع اللجنة الشهري", "user": "د. سارة المحمود"}
    ]

    for activity in activities:
        with st.container():
            col1, col2 = st.columns([3, 1])
            with col1:
                st.markdown(f"**{activity['activity']}**")
                st.caption(f"بواسطة {activity['user']}")
            with col2:
                st.caption(activity['time'])
            st.divider()

def display_upcoming_deadlines(tasks_df):
    """Display upcoming deadlines"""
    st.subheader("⏰ المواعيد النهائية القادمة")

    if tasks_df.empty:
        st.info("لا توجد مواعيد نهائية قادمة")
        return

    # Convert deadline to datetime and filter upcoming
    tasks_df['deadline'] = pd.to_datetime(tasks_df['deadline'])
    today = datetime.now()
    upcoming = tasks_df[
        (tasks_df['deadline'] >= today) &
        (tasks_df['status'] != 'مكتملة')
    ].sort_values('deadline').head(5)

    for _, task in upcoming.iterrows():
        days_left = (task['deadline'] - today).days

        if days_left <= 7:
            urgency_color = "🔴"
        elif days_left <= 30:
            urgency_color = "🟡"
        else:
            urgency_color = "🟢"

        st.write(f"{urgency_color} **مهمة {task['task_number']}**: {task['title']}")
        st.caption(f"الموعد النهائي: {task['deadline'].strftime('%Y-%m-%d')} ({days_left} يوم متبقي)")
        st.divider()

def display_lab_status_overview(labs_df):
    """Display laboratory status overview"""
    st.subheader("🔬 نظرة عامة على حالة المختبرات")

    if labs_df.empty:
        st.info("لا توجد بيانات مختبرات متاحة")
        return

    # Status distribution
    status_counts = labs_df['status'].value_counts()

    col1, col2 = st.columns(2)

    with col1:
        # Status pie chart
        fig = px.pie(
            values=status_counts.values,
            names=status_counts.index,
            title="توزيع حالة المختبرات",
            color_discrete_map={
                'تشغيلي': '#90ee90',
                'تحت الصيانة': '#ffb347',
                'خارج الخدمة': '#ff7f7f'
            }
        )
        fig.update_layout(height=300, font=dict(family="Noto Sans Arabic"))
        st.plotly_chart(fig, use_container_width=True)

    with col2:
        # Lab details table with Arabic headers
        labs_display = labs_df[['lab_name', 'status', 'capacity']].copy()
        labs_display.columns = ['اسم المختبر', 'الحالة', 'السعة']
        st.dataframe(
            labs_display,
            use_container_width=True,
            hide_index=True
        )
