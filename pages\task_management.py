import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go

def show_task_management():
    """Enhanced task management page"""
    st.title("📋 إدارة المهام")

    db_manager = st.session_state.db_manager
    tasks_df = db_manager.get_tasks_summary()

    # Task overview metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("إجمالي المهام", len(tasks_df))

    with col2:
        completed = len(tasks_df[tasks_df['status'] == 'مكتملة'])
        completion_rate = (completed / len(tasks_df) * 100) if len(tasks_df) > 0 else 0
        st.metric("مكتملة", completed, f"{completion_rate:.1f}%")

    with col3:
        in_progress = len(tasks_df[tasks_df['status'] == 'قيد التنفيذ'])
        st.metric("قيد التنفيذ", in_progress)

    with col4:
        overdue = len(tasks_df[
            (pd.to_datetime(tasks_df['deadline']) < datetime.now()) &
            (tasks_df['status'] != 'مكتملة')
        ])
        st.metric("متأخرة", overdue, delta_color="inverse")
    
    st.divider()
    
    # Task filters
    col1, col2, col3 = st.columns(3)

    with col1:
        status_filter = st.selectbox(
            "تصفية حسب الحالة",
            ["الكل"] + list(tasks_df['status'].unique()),
            key="task_status_filter"
        )

    with col2:
        # Priority filter (if we had priority data)
        priority_filter = st.selectbox(
            "تصفية حسب الأولوية",
            ["الكل", "عالية", "متوسطة", "منخفضة"],
            key="task_priority_filter"
        )

    with col3:
        # Date range filter
        date_filter = st.selectbox(
            "تصفية حسب الموعد النهائي",
            ["الكل", "هذا الأسبوع", "هذا الشهر", "متأخرة"],
            key="task_date_filter"
        )
    
    # Apply filters
    filtered_tasks = tasks_df.copy()

    if status_filter != "الكل":
        filtered_tasks = filtered_tasks[filtered_tasks['status'] == status_filter]

    if date_filter == "هذا الأسبوع":
        next_week = datetime.now() + timedelta(days=7)
        filtered_tasks = filtered_tasks[
            pd.to_datetime(filtered_tasks['deadline']) <= next_week
        ]
    elif date_filter == "هذا الشهر":
        next_month = datetime.now() + timedelta(days=30)
        filtered_tasks = filtered_tasks[
            pd.to_datetime(filtered_tasks['deadline']) <= next_month
        ]
    elif date_filter == "متأخرة":
        filtered_tasks = filtered_tasks[
            (pd.to_datetime(filtered_tasks['deadline']) < datetime.now()) &
            (filtered_tasks['status'] != 'مكتملة')
        ]
    
    st.divider()
    
    # Task visualization
    col1, col2 = st.columns(2)

    with col1:
        # Status distribution
        if not filtered_tasks.empty:
            status_counts = filtered_tasks['status'].value_counts()
            fig = px.pie(
                values=status_counts.values,
                names=status_counts.index,
                title="توزيع حالة المهام",
                color_discrete_map={
                    'لم تبدأ': '#ff7f7f',
                    'قيد التنفيذ': '#ffb347',
                    'مكتملة': '#90ee90',
                    'معلقة': '#d3d3d3'
                }
            )
            fig.update_layout(font=dict(family="Noto Sans Arabic"))
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("لا توجد مهام تطابق المرشحات المحددة")

    with col2:
        # Hours allocation by status
        if not filtered_tasks.empty:
            hours_by_status = filtered_tasks.groupby('status')['estimated_hours'].sum().reset_index()
            fig = px.bar(
                hours_by_status,
                x='status',
                y='estimated_hours',
                title="توزيع الساعات المقدرة حسب الحالة",
                labels={'estimated_hours': 'الساعات المقدرة', 'status': 'الحالة'}
            )
            fig.update_layout(font=dict(family="Noto Sans Arabic"))
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("لا توجد بيانات ساعات متاحة")
    
    st.divider()
    
    # Detailed task list
    st.subheader("📝 تفاصيل المهام")

    # Task cards
    for _, task in filtered_tasks.iterrows():
        with st.expander(f"مهمة {task['task_number']}: {task['title']}", expanded=False):

            # Task header info
            col1, col2, col3 = st.columns(3)

            with col1:
                st.write(f"**الحالة:** {task['status']}")

            with col2:
                deadline = pd.to_datetime(task['deadline'])
                days_left = (deadline - datetime.now()).days
                if days_left < 0:
                    st.write(f"**الموعد النهائي:** {deadline.strftime('%Y-%m-%d')} ⚠️ ({abs(days_left)} يوم متأخر)")
                else:
                    st.write(f"**الموعد النهائي:** {deadline.strftime('%Y-%m-%d')} ({days_left} يوم متبقي)")

            with col3:
                st.write(f"**الساعات المقدرة:** {task['estimated_hours']}")
            
            st.divider()
            
            # Task activities (from the workflow table)
            task_activities = get_task_activities(task['task_number'])
            if task_activities:
                st.write("**الأنشطة:**")
                for activity in task_activities:
                    st.write(f"• {activity}")

            # Task deliverables (from the workflow table)
            task_deliverables = get_task_deliverables(task['task_number'])
            if task_deliverables:
                st.write("**المخرجات:**")
                for deliverable in task_deliverables:
                    st.write(f"• {deliverable}")

            st.divider()

            # Status update section
            col1, col2 = st.columns([2, 1])

            with col1:
                new_status = st.selectbox(
                    "تحديث الحالة",
                    ["لم تبدأ", "قيد التنفيذ", "مكتملة", "معلقة"],
                    index=["لم تبدأ", "قيد التنفيذ", "مكتملة", "معلقة"].index(task['status']),
                    key=f"status_{task['task_number']}"
                )

            with col2:
                if st.button(f"تحديث الحالة", key=f"update_{task['task_number']}"):
                    if new_status != task['status']:
                        # Update in database
                        db_manager.update_task_status(task['task_number'], new_status)
                        st.success(f"تم تحديث حالة المهمة {task['task_number']} إلى {new_status}")
                        st.rerun()

            # Progress notes
            st.text_area(
                "ملاحظات التقدم",
                placeholder="أضف ملاحظات التقدم أو التعليقات...",
                key=f"notes_{task['task_number']}"
            )

            # File upload for deliverables
            uploaded_file = st.file_uploader(
                "رفع المخرجات",
                key=f"upload_{task['task_number']}",
                help="رفع المستندات والتقارير أو المخرجات الأخرى"
            )

            if uploaded_file:
                st.success(f"تم رفع الملف '{uploaded_file.name}' بنجاح!")

def get_task_activities(task_number):
    """Get activities for a specific task based on the workflow"""
    activities_map = {
        1: [
            "جمع بيانات الجرد الحالية",
            "تصميم/تحديث هيكل قاعدة البيانات",
            "إدخال البيانات والتحقق منها",
            "التنسيق مع إدارة المعدات"
        ],
        2: [
            "إدراج الموردين الحاليين",
            "جمع معلومات الموردين المفقودة",
            "إدخال البيانات في قاعدة البيانات",
            "جدولة التحديثات نصف السنوية"
        ],
        3: [
            "جمع طلبات الصيانة من المشرفين",
            "فحص المختبرات (ملاحظة المختبرات المعطلة)",
            "ترتيب الاحتياجات العاجلة حسب الأولوية",
            "إعداد خطة الصيانة"
        ],
        4: [
            "مراجعة معايير الاعتماد",
            "استشارة أعضاء هيئة التدريس للاحتياجات",
            "صياغة مقترحات التطوير",
            "تقديم للحصول على التغذية الراجعة",
            "الانتهاء والتقديم للكلية"
        ],
        5: [
            "تحديد المقررات المتأثرة بالمختبرات المعطلة",
            "تطوير خطط التدريس البديلة",
            "إيصال الخطط لأعضاء هيئة التدريس/الطلاب",
            "مراقبة التنفيذ"
        ],
        6: [
            "مراجعة السياسات/إجراءات التشغيل الموجودة",
            "صياغة التحديثات (تشمل الإجراءات البديلة)",
            "تعميم للحصول على التغذية الراجعة",
            "الموافقة وتوزيع التحديثات"
        ],
        7: [
            "مراجعة بروتوكولات السلامة",
            "إجراء تدقيقات السلامة الفصلية",
            "معالجة القضايا العاجلة",
            "توثيق الامتثال"
        ],
        8: [
            "التخطيط للتدريب على المعدات الجديدة/البدائل",
            "جدولة الجلسات",
            "إجراء وتوثيق التدريب",
            "جمع التغذية الراجعة"
        ],
        9: [
            "إعداد طلبات المعدات/الإمدادات السنوية",
            "التقديم للمشتريات",
            "تتبع حالة الطلب",
            "تحديث الجرد عند الاستلام"
        ],
        10: [
            "متابعة طلبات الصيانة",
            "التحقق من الإنجاز",
            "تحديث السجلات",
            "الإبلاغ عن القضايا غير المحلولة"
        ],
        11: [
            "جمع بيانات الاستخدام",
            "تحليل الكفاءة",
            "التوصية بالتحسينات"
        ],
        12: [
            "تجميع الإنجازات والتحديات",
            "تلخيص الصيانة والمشتريات والتكامل الأكاديمي",
            "اقتراح خطة التحسين",
            "التقديم للقسم"
        ],
        13: [
            "جدولة الاجتماعات المنتظمة (شهرياً)",
            "تسجيل المحاضر",
            "تقديم للقسم بعد كل اجتماع"
        ],
        14: [
            "جمع التغذية الراجعة من أعضاء هيئة التدريس والموظفين والطلاب",
            "مراجعة وتحديث السياسات للعام القادم",
            "عرض التحديثات على اللجنة"
        ]
    }
    
    return activities_map.get(task_number, [])

def get_task_deliverables(task_number):
    """Get deliverables for a specific task based on the workflow"""
    deliverables_map = {
        1: ["ورقة جمع بيانات الجرد", "قاعدة البيانات المكتملة والمتحقق منها"],
        2: ["ملف قاعدة بيانات الموردين", "جدول التحديث"],
        3: ["سجل طلبات الصيانة", "قائمة المختبرات المعطلة", "خطة عمل الصيانة"],
        4: ["المقترح الأولي", "وثيقة المقترح النهائي"],
        5: ["خطة التدريس البديلة", "تقرير التنفيذ"],
        6: ["إجراءات التشغيل/الأدلة المحدثة", "سجل التوزيع"],
        7: ["تقارير التدقيق", "سجلات الإجراءات التصحيحية"],
        8: ["أوراق حضور التدريب", "ملخصات التغذية الراجعة"],
        9: ["نماذج الطلبات", "ورقة تتبع المشتريات", "الجرد المحدث"],
        10: ["سجل إنجاز الصيانة", "تقرير الحالة"],
        11: ["تقرير الاستخدام", "توصيات التحسين"],
        12: ["التقرير السنوي", "خطة التحسين"],
        13: ["محاضر الاجتماعات"],
        14: ["ملخص التغذية الراجعة", "مسودة السياسة المحدثة"]
    }

    return deliverables_map.get(task_number, [])
