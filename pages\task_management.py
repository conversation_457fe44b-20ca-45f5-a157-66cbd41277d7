import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go

def show_task_management():
    """Enhanced task management page"""
    st.title("📋 Task Management")
    
    db_manager = st.session_state.db_manager
    tasks_df = db_manager.get_tasks_summary()
    
    # Task overview metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Tasks", len(tasks_df))
    
    with col2:
        completed = len(tasks_df[tasks_df['status'] == 'Completed'])
        completion_rate = (completed / len(tasks_df) * 100) if len(tasks_df) > 0 else 0
        st.metric("Completed", completed, f"{completion_rate:.1f}%")
    
    with col3:
        in_progress = len(tasks_df[tasks_df['status'] == 'In Progress'])
        st.metric("In Progress", in_progress)
    
    with col4:
        overdue = len(tasks_df[
            (pd.to_datetime(tasks_df['deadline']) < datetime.now()) & 
            (tasks_df['status'] != 'Completed')
        ])
        st.metric("Overdue", overdue, delta_color="inverse")
    
    st.divider()
    
    # Task filters
    col1, col2, col3 = st.columns(3)
    
    with col1:
        status_filter = st.selectbox(
            "Filter by Status",
            ["All"] + list(tasks_df['status'].unique()),
            key="task_status_filter"
        )
    
    with col2:
        # Priority filter (if we had priority data)
        priority_filter = st.selectbox(
            "Filter by Priority",
            ["All", "High", "Medium", "Low"],
            key="task_priority_filter"
        )
    
    with col3:
        # Date range filter
        date_filter = st.selectbox(
            "Filter by Deadline",
            ["All", "This Week", "This Month", "Overdue"],
            key="task_date_filter"
        )
    
    # Apply filters
    filtered_tasks = tasks_df.copy()
    
    if status_filter != "All":
        filtered_tasks = filtered_tasks[filtered_tasks['status'] == status_filter]
    
    if date_filter == "This Week":
        next_week = datetime.now() + timedelta(days=7)
        filtered_tasks = filtered_tasks[
            pd.to_datetime(filtered_tasks['deadline']) <= next_week
        ]
    elif date_filter == "This Month":
        next_month = datetime.now() + timedelta(days=30)
        filtered_tasks = filtered_tasks[
            pd.to_datetime(filtered_tasks['deadline']) <= next_month
        ]
    elif date_filter == "Overdue":
        filtered_tasks = filtered_tasks[
            (pd.to_datetime(filtered_tasks['deadline']) < datetime.now()) & 
            (filtered_tasks['status'] != 'Completed')
        ]
    
    st.divider()
    
    # Task visualization
    col1, col2 = st.columns(2)
    
    with col1:
        # Status distribution
        if not filtered_tasks.empty:
            status_counts = filtered_tasks['status'].value_counts()
            fig = px.pie(
                values=status_counts.values,
                names=status_counts.index,
                title="Task Status Distribution",
                color_discrete_map={
                    'Not Started': '#ff7f7f',
                    'In Progress': '#ffb347',
                    'Completed': '#90ee90',
                    'On Hold': '#d3d3d3'
                }
            )
            st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # Budget allocation by status
        if not filtered_tasks.empty:
            budget_by_status = filtered_tasks.groupby('status')['budget_allocation'].sum().reset_index()
            fig = px.bar(
                budget_by_status,
                x='status',
                y='budget_allocation',
                title="Budget Allocation by Status",
                labels={'budget_allocation': 'Budget (SAR)', 'status': 'Status'}
            )
            st.plotly_chart(fig, use_container_width=True)
    
    st.divider()
    
    # Detailed task list
    st.subheader("📝 Task Details")
    
    # Task cards
    for _, task in filtered_tasks.iterrows():
        with st.expander(f"Task {task['task_number']}: {task['title']}", expanded=False):
            
            # Task header info
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.write(f"**Status:** {task['status']}")
            
            with col2:
                deadline = pd.to_datetime(task['deadline'])
                days_left = (deadline - datetime.now()).days
                if days_left < 0:
                    st.write(f"**Deadline:** {deadline.strftime('%Y-%m-%d')} ⚠️ ({abs(days_left)} days overdue)")
                else:
                    st.write(f"**Deadline:** {deadline.strftime('%Y-%m-%d')} ({days_left} days left)")
            
            with col3:
                st.write(f"**Estimated Hours:** {task['estimated_hours']}")
            
            with col4:
                st.write(f"**Budget:** SAR {task['budget_allocation']:,.0f}")
            
            st.divider()
            
            # Task activities (from the workflow table)
            task_activities = get_task_activities(task['task_number'])
            if task_activities:
                st.write("**Activities:**")
                for activity in task_activities:
                    st.write(f"• {activity}")
            
            # Task deliverables (from the workflow table)
            task_deliverables = get_task_deliverables(task['task_number'])
            if task_deliverables:
                st.write("**Deliverables:**")
                for deliverable in task_deliverables:
                    st.write(f"• {deliverable}")
            
            st.divider()
            
            # Status update section
            col1, col2 = st.columns([2, 1])
            
            with col1:
                new_status = st.selectbox(
                    "Update Status",
                    ["Not Started", "In Progress", "Completed", "On Hold"],
                    index=["Not Started", "In Progress", "Completed", "On Hold"].index(task['status']),
                    key=f"status_{task['task_number']}"
                )
            
            with col2:
                if st.button(f"Update Status", key=f"update_{task['task_number']}"):
                    if new_status != task['status']:
                        # Update in database
                        db_manager.update_task_status(task['task_number'], new_status)
                        st.success(f"Task {task['task_number']} status updated to {new_status}")
                        st.rerun()
            
            # Progress notes
            st.text_area(
                "Progress Notes",
                placeholder="Add progress notes or comments...",
                key=f"notes_{task['task_number']}"
            )
            
            # File upload for deliverables
            uploaded_file = st.file_uploader(
                "Upload Deliverable",
                key=f"upload_{task['task_number']}",
                help="Upload documents, reports, or other deliverables"
            )
            
            if uploaded_file:
                st.success(f"File '{uploaded_file.name}' uploaded successfully!")

def get_task_activities(task_number):
    """Get activities for a specific task based on the workflow"""
    activities_map = {
        1: [
            "Collect current inventory data",
            "Design/update database structure", 
            "Enter and verify data",
            "Coordinate with equipment administration"
        ],
        2: [
            "List current suppliers",
            "Gather missing supplier info",
            "Enter data into database",
            "Schedule biannual updates"
        ],
        3: [
            "Collect maintenance requests from supervisors",
            "Inspect labs (note out-of-order labs)",
            "Prioritize urgent needs",
            "Compile maintenance plan"
        ],
        4: [
            "Review accreditation standards",
            "Consult faculty for needs",
            "Draft development proposals",
            "Submit for feedback",
            "Finalize and Submit to college"
        ],
        5: [
            "Identify courses affected by out-of-order labs",
            "Develop alternative teaching plans",
            "Communicate plans to faculty/students",
            "Monitor implementation"
        ],
        6: [
            "Review existing policies/SOPs",
            "Draft updates (include alternative procedures)",
            "Circulate for feedback",
            "Approve and distribute updates"
        ],
        7: [
            "Review safety protocols",
            "Conduct quarterly safety audits",
            "Address urgent issues",
            "Document compliance"
        ],
        8: [
            "Plan training for new equipment/alternatives",
            "Schedule sessions",
            "Conduct and document training",
            "Collect feedback"
        ],
        9: [
            "Prepare annual equipment/supply orders",
            "Submit to procurement",
            "Track order status",
            "Update inventory upon receipt"
        ],
        10: [
            "Follow up on maintenance requests",
            "Verify completion",
            "Update records",
            "Report unresolved issues"
        ],
        11: [
            "Collect usage data",
            "Analyze efficiency",
            "Recommend improvements"
        ],
        12: [
            "Compile achievements and challenges",
            "Summarize maintenance, procurement, and academic integration",
            "Propose improvement plan",
            "Submit to department"
        ],
        13: [
            "Schedule regular meetings (monthly)",
            "Record minutes",
            "Submit to department after each meeting"
        ],
        14: [
            "Collect feedback from faculty, staff, students",
            "Review and update policies for next year",
            "Present updates to committee"
        ]
    }
    
    return activities_map.get(task_number, [])

def get_task_deliverables(task_number):
    """Get deliverables for a specific task based on the workflow"""
    deliverables_map = {
        1: ["Inventory data collection sheet", "Completed and verified database"],
        2: ["Supplier database file", "Update schedule"],
        3: ["Maintenance request log", "List of out-of-order labs", "Maintenance action plan"],
        4: ["Draft proposal", "Final proposal document"],
        5: ["Alternative teaching plan", "Implementation report"],
        6: ["Updated SOPs/manuals", "Distribution record"],
        7: ["Audit reports", "Corrective action records"],
        8: ["Training attendance sheets", "Feedback summaries"],
        9: ["Order forms", "Procurement tracking sheet", "Updated inventory"],
        10: ["Maintenance completion log", "Status report"],
        11: ["Utilization report", "Improvement recommendations"],
        12: ["Annual report", "Improvement plan"],
        13: ["Meeting minutes"],
        14: ["Feedback summary", "Updated policy draft"]
    }
    
    return deliverables_map.get(task_number, [])
