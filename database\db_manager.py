import sqlite3
import os
import pandas as pd
from datetime import datetime, date
import hashlib
import bcrypt

class DatabaseManager:
    def __init__(self, db_path="database/committee_system.db"):
        self.db_path = db_path
        self.ensure_database_exists()
        self.init_database()
    
    def ensure_database_exists(self):
        """Ensure the database directory exists"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    def get_connection(self):
        """Get database connection"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """Initialize database with schema"""
        try:
            with open('database/schema.sql', 'r') as f:
                schema = f.read()
            
            conn = self.get_connection()
            conn.executescript(schema)
            conn.commit()
            conn.close()
            
            # Initialize with sample data if empty
            self.init_sample_data()
            
        except Exception as e:
            print(f"Error initializing database: {e}")
    
    def init_sample_data(self):
        """Initialize database with sample data"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Check if users exist
        cursor.execute("SELECT COUNT(*) FROM users")
        if cursor.fetchone()[0] == 0:
            self.create_sample_users(conn)
            self.create_sample_tasks(conn)
            self.create_sample_labs(conn)
            self.create_sample_suppliers(conn)
        
        conn.close()
    
    def create_sample_users(self, conn):
        """Create sample users"""
        users = [
            ('admin', '<EMAIL>', 'Dr. Ahmed Al-Rashid', 'Diagnostic Radiology', 'admin'),
            ('chair', '<EMAIL>', 'Dr. Sarah Al-Mahmoud', 'Diagnostic Radiology', 'chair'),
            ('member1', '<EMAIL>', 'Dr. Mohammed Al-Qasim', 'Applied Medical Sciences', 'member'),
            ('supervisor1', '<EMAIL>', 'Eng. Fatima Al-Zahra', 'Lab Operations', 'supervisor'),
            ('faculty1', '<EMAIL>', 'Dr. Omar Al-Hashimi', 'Diagnostic Radiology', 'faculty')
        ]
        
        for username, email, full_name, department, role_type in users:
            # Hash password (default: same as username)
            password_hash = bcrypt.hashpw(username.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO users (username, email, password_hash, full_name, department)
                VALUES (?, ?, ?, ?, ?)
            """, (username, email, password_hash, full_name, department))
            
            user_id = cursor.lastrowid
            
            # Assign role
            role_mapping = {
                'admin': 5, 'chair': 1, 'member': 2, 'supervisor': 3, 'faculty': 4
            }
            
            cursor.execute("""
                INSERT INTO user_roles (user_id, role_id)
                VALUES (?, ?)
            """, (user_id, role_mapping[role_type]))
        
        conn.commit()
    
    def create_sample_tasks(self, conn):
        """Create the 14 scheduled tasks from the workflow"""
        tasks = [
            (1, "إنشاء وتحديث قاعدة بيانات المختبرات المهارية", "2025-09-30", 80),
            (2, "إنشاء/تحديث قاعدة بيانات الموردين", "2025-10-10", 40),
            (3, "تقييم احتياجات المختبرات والصيانة", "2025-10-20", 120),
            (4, "اقتراح خطط تطوير/ترقية المختبرات", "2025-11-05", 160),
            (5, "التكامل الأكاديمي والبدائل", "2025-11-20", 100),
            (6, "تحديث سياسات المختبرات وإجراءات التشغيل والأدلة", "2025-12-10", 80),
            (7, "مراقبة الامتثال للصحة والسلامة", "2025-12-20", 60),
            (8, "تنظيم التدريب والدعم", "2026-01-10", 40),
            (9, "إدارة المشتريات والتوريد", "2026-01-25", 100),
            (10, "الإشراف على تنفيذ الصيانة", "2026-02-10", 80),
            (11, "مراجعة استخدام المختبرات والكفاءة", "2026-02-28", 60),
            (12, "إعداد وتقديم التقرير السنوي", "2026-03-31", 120),
            (13, "تقديم محاضر الاجتماعات", "2026-04-30", 200),
            (14, "ملاحظات أصحاب المصلحة وتحسين السياسات", "2026-04-15", 80)
        ]

        cursor = conn.cursor()
        for task_num, title, deadline, hours in tasks:
            cursor.execute("""
                INSERT INTO tasks (task_number, title, deadline, estimated_hours, status)
                VALUES (?, ?, ?, ?, 'لم تبدأ')
            """, (task_num, title, deadline, hours))
        
        conn.commit()
    
    def create_sample_labs(self, conn):
        """Create sample laboratory data"""
        labs = [
            ("مختبر الأشعة السينية", "XRAY-01", "المبنى أ، الطابق الثاني", 20, "تشغيلي"),
            ("مختبر الأشعة المقطعية", "CT-01", "المبنى أ، الطابق الأول", 15, "تشغيلي"),
            ("مختبر الرنين المغناطيسي", "MRI-01", "المبنى ب، الطابق الأول", 10, "تحت الصيانة"),
            ("مختبر الموجات فوق الصوتية", "US-01", "المبنى أ، الطابق الثالث", 25, "تشغيلي"),
            ("مختبر الطب النووي", "NM-01", "المبنى ج، الطابق الأول", 12, "تشغيلي")
        ]
        
        cursor = conn.cursor()
        for lab_name, lab_code, location, capacity, status in labs:
            cursor.execute("""
                INSERT INTO laboratories (lab_name, lab_code, location, capacity, status, last_inspection)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (lab_name, lab_code, location, capacity, status, '2024-09-01'))
        
        conn.commit()
    
    def create_sample_suppliers(self, conn):
        """Create sample supplier data"""
        suppliers = [
            ("Siemens Healthineers", "Ahmed Al-Rashid", "<EMAIL>", "+966-11-123-4567", "Riyadh, Saudi Arabia", "Medical Imaging Equipment", 4.5),
            ("GE Healthcare", "Sarah Johnson", "<EMAIL>", "+966-11-234-5678", "Jeddah, Saudi Arabia", "Diagnostic Equipment", 4.3),
            ("Philips Healthcare", "Mohammed Al-Qasim", "<EMAIL>", "+966-11-345-6789", "Dammam, Saudi Arabia", "Healthcare Technology", 4.4),
            ("Canon Medical", "Fatima Al-Zahra", "<EMAIL>", "+966-11-456-7890", "Riyadh, Saudi Arabia", "Medical Imaging", 4.2),
            ("Fujifilm Healthcare", "Omar Al-Hashimi", "<EMAIL>", "+966-11-567-8901", "Jeddah, Saudi Arabia", "Diagnostic Imaging", 4.1)
        ]
        
        cursor = conn.cursor()
        for name, contact, email, phone, address, specialization, rating in suppliers:
            cursor.execute("""
                INSERT INTO suppliers (supplier_name, contact_person, email, phone, address, specialization, rating)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (name, contact, email, phone, address, specialization, rating))
        
        conn.commit()
    
    def authenticate_user(self, username, password):
        """Authenticate user login"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT u.id, u.username, u.password_hash, u.full_name, u.email, u.department,
                   r.role_name
            FROM users u
            JOIN user_roles ur ON u.id = ur.user_id
            JOIN roles r ON ur.role_id = r.id
            WHERE u.username = ? AND u.is_active = TRUE
        """, (username,))
        
        user = cursor.fetchone()
        conn.close()
        
        if user and bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
            return {
                'id': user['id'],
                'username': user['username'],
                'full_name': user['full_name'],
                'email': user['email'],
                'department': user['department'],
                'role': user['role_name']
            }
        return None
    
    def get_tasks_summary(self):
        """Get tasks summary for dashboard"""
        conn = self.get_connection()
        df = pd.read_sql_query("""
            SELECT task_number, title, deadline, status, estimated_hours
            FROM tasks
            ORDER BY task_number
        """, conn)
        conn.close()
        return df
    
    def get_labs_summary(self):
        """Get laboratories summary"""
        conn = self.get_connection()
        df = pd.read_sql_query("""
            SELECT lab_name, lab_code, location, capacity, status, last_inspection
            FROM laboratories
            ORDER BY lab_name
        """, conn)
        conn.close()
        return df
    
    def update_task_status(self, task_id, status):
        """Update task status"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE tasks SET status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """, (status, task_id))
        conn.commit()
        conn.close()
    
    def get_kpi_data(self):
        """Get KPI data for dashboard"""
        conn = self.get_connection()

        # Task completion stats
        cursor = conn.cursor()
        cursor.execute("""
            SELECT status, COUNT(*) as count
            FROM tasks
            GROUP BY status
        """)
        task_stats = dict(cursor.fetchall())

        # Lab operational stats
        cursor.execute("""
            SELECT status, COUNT(*) as count
            FROM laboratories
            GROUP BY status
        """)
        lab_stats = dict(cursor.fetchall())



        conn.close()

        return {
            'task_stats': task_stats,
            'lab_stats': lab_stats
        }

    def get_suppliers_summary(self):
        """Get suppliers summary"""
        conn = self.get_connection()
        df = pd.read_sql_query("""
            SELECT supplier_name, contact_person, email, phone, specialization, rating, is_active
            FROM suppliers
            ORDER BY supplier_name
        """, conn)
        conn.close()
        return df

    def get_maintenance_requests(self):
        """Get maintenance requests"""
        conn = self.get_connection()
        df = pd.read_sql_query("""
            SELECT mr.id, l.lab_name, e.equipment_name, mr.request_type,
                   mr.description, mr.priority, mr.status, mr.requested_at,
                   u.full_name as requested_by_name
            FROM maintenance_requests mr
            JOIN equipment e ON mr.equipment_id = e.id
            JOIN laboratories l ON e.lab_id = l.id
            JOIN users u ON mr.requested_by = u.id
            ORDER BY mr.requested_at DESC
        """, conn)
        conn.close()
        return df

    def add_maintenance_request(self, equipment_id, request_type, description, priority, requested_by):
        """Add new maintenance request"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO maintenance_requests (equipment_id, request_type, description, priority, requested_by)
            VALUES (?, ?, ?, ?, ?)
        """, (equipment_id, request_type, description, priority, requested_by))
        conn.commit()
        conn.close()

    def get_equipment_by_lab(self, lab_id=None):
        """Get equipment list, optionally filtered by lab"""
        conn = self.get_connection()
        if lab_id:
            df = pd.read_sql_query("""
                SELECT e.id, e.equipment_name, e.model, e.serial_number,
                       e.manufacturer, e.status, l.lab_name
                FROM equipment e
                JOIN laboratories l ON e.lab_id = l.id
                WHERE e.lab_id = ?
                ORDER BY e.equipment_name
            """, conn, params=(lab_id,))
        else:
            df = pd.read_sql_query("""
                SELECT e.id, e.equipment_name, e.model, e.serial_number,
                       e.manufacturer, e.status, l.lab_name
                FROM equipment e
                JOIN laboratories l ON e.lab_id = l.id
                ORDER BY l.lab_name, e.equipment_name
            """, conn)
        conn.close()
        return df
