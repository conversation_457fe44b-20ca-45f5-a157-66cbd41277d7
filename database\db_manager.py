import sqlite3
import os
import pandas as pd
from datetime import datetime, date
import hashlib
import bcrypt

class DatabaseManager:
    def __init__(self, db_path="database/committee_system.db"):
        self.db_path = db_path
        self.ensure_database_exists()
        self.init_database()
    
    def ensure_database_exists(self):
        """Ensure the database directory exists"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    def get_connection(self):
        """Get database connection"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """Initialize database with schema"""
        try:
            with open('database/schema.sql', 'r') as f:
                schema = f.read()
            
            conn = self.get_connection()
            conn.executescript(schema)
            conn.commit()
            conn.close()
            
            # Initialize with sample data if empty
            self.init_sample_data()
            
        except Exception as e:
            print(f"Error initializing database: {e}")
    
    def init_sample_data(self):
        """Initialize database with sample data"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Check if users exist
        cursor.execute("SELECT COUNT(*) FROM users")
        if cursor.fetchone()[0] == 0:
            self.create_sample_users(conn)
            self.create_sample_tasks(conn)
            self.create_sample_labs(conn)
            self.create_sample_suppliers(conn)
        
        conn.close()
    
    def create_sample_users(self, conn):
        """Create sample users"""
        users = [
            ('admin', '<EMAIL>', 'Dr. Ahmed Al-Rashid', 'Diagnostic Radiology', 'admin'),
            ('chair', '<EMAIL>', 'Dr. Sarah Al-Mahmoud', 'Diagnostic Radiology', 'chair'),
            ('member1', '<EMAIL>', 'Dr. Mohammed Al-Qasim', 'Applied Medical Sciences', 'member'),
            ('supervisor1', '<EMAIL>', 'Eng. Fatima Al-Zahra', 'Lab Operations', 'supervisor'),
            ('faculty1', '<EMAIL>', 'Dr. Omar Al-Hashimi', 'Diagnostic Radiology', 'faculty')
        ]
        
        for username, email, full_name, department, role_type in users:
            # Hash password (default: same as username)
            password_hash = bcrypt.hashpw(username.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO users (username, email, password_hash, full_name, department)
                VALUES (?, ?, ?, ?, ?)
            """, (username, email, password_hash, full_name, department))
            
            user_id = cursor.lastrowid
            
            # Assign role
            role_mapping = {
                'admin': 5, 'chair': 1, 'member': 2, 'supervisor': 3, 'faculty': 4
            }
            
            cursor.execute("""
                INSERT INTO user_roles (user_id, role_id)
                VALUES (?, ?)
            """, (user_id, role_mapping[role_type]))
        
        conn.commit()
    
    def create_sample_tasks(self, conn):
        """Create the 14 scheduled tasks from the workflow"""
        tasks = [
            (1, "Establish and update skills lab database", "2025-09-30", 80, 25000),
            (2, "Establish/update supplier database", "2025-10-10", 40, 15000),
            (3, "Assess lab needs & maintenance", "2025-10-20", 120, 50000),
            (4, "Propose lab development/upgrade plans", "2025-11-05", 160, 100000),
            (5, "Academic integration & alternatives", "2025-11-20", 100, 30000),
            (6, "Update lab policies, SOPs, and manuals", "2025-12-10", 80, 20000),
            (7, "Monitor health & safety compliance", "2025-12-20", 60, 25000),
            (8, "Organize training & support", "2026-01-10", 40, 15000),
            (9, "Manage procurement & supply", "2026-01-25", 100, 200000),
            (10, "Oversee maintenance execution", "2026-02-10", 80, 75000),
            (11, "Review lab utilization & efficiency", "2026-02-28", 60, 20000),
            (12, "Prepare and submit annual report", "2026-03-31", 120, 30000),
            (13, "Submit meeting minutes", "2026-04-30", 200, 10000),
            (14, "Stakeholder feedback & policy improvement", "2026-04-15", 80, 25000)
        ]
        
        cursor = conn.cursor()
        for task_num, title, deadline, hours, budget in tasks:
            cursor.execute("""
                INSERT INTO tasks (task_number, title, deadline, estimated_hours, budget_allocation, status)
                VALUES (?, ?, ?, ?, ?, 'Not Started')
            """, (task_num, title, deadline, hours, budget))
        
        conn.commit()
    
    def create_sample_labs(self, conn):
        """Create sample laboratory data"""
        labs = [
            ("X-Ray Laboratory", "XRAY-01", "Building A, Floor 2", 20, "Operational"),
            ("CT Scan Laboratory", "CT-01", "Building A, Floor 1", 15, "Operational"),
            ("MRI Laboratory", "MRI-01", "Building B, Floor 1", 10, "Under Maintenance"),
            ("Ultrasound Laboratory", "US-01", "Building A, Floor 3", 25, "Operational"),
            ("Nuclear Medicine Lab", "NM-01", "Building C, Floor 1", 12, "Operational")
        ]
        
        cursor = conn.cursor()
        for lab_name, lab_code, location, capacity, status in labs:
            cursor.execute("""
                INSERT INTO laboratories (lab_name, lab_code, location, capacity, status, last_inspection)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (lab_name, lab_code, location, capacity, status, '2024-09-01'))
        
        conn.commit()
    
    def create_sample_suppliers(self, conn):
        """Create sample supplier data"""
        suppliers = [
            ("Siemens Healthineers", "Ahmed Al-Rashid", "<EMAIL>", "+966-11-123-4567", "Riyadh, Saudi Arabia", "Medical Imaging Equipment", 4.5),
            ("GE Healthcare", "Sarah Johnson", "<EMAIL>", "+966-11-234-5678", "Jeddah, Saudi Arabia", "Diagnostic Equipment", 4.3),
            ("Philips Healthcare", "Mohammed Al-Qasim", "<EMAIL>", "+966-11-345-6789", "Dammam, Saudi Arabia", "Healthcare Technology", 4.4),
            ("Canon Medical", "Fatima Al-Zahra", "<EMAIL>", "+966-11-456-7890", "Riyadh, Saudi Arabia", "Medical Imaging", 4.2),
            ("Fujifilm Healthcare", "Omar Al-Hashimi", "<EMAIL>", "+966-11-567-8901", "Jeddah, Saudi Arabia", "Diagnostic Imaging", 4.1)
        ]
        
        cursor = conn.cursor()
        for name, contact, email, phone, address, specialization, rating in suppliers:
            cursor.execute("""
                INSERT INTO suppliers (supplier_name, contact_person, email, phone, address, specialization, rating)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (name, contact, email, phone, address, specialization, rating))
        
        conn.commit()
    
    def authenticate_user(self, username, password):
        """Authenticate user login"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT u.id, u.username, u.password_hash, u.full_name, u.email, u.department,
                   r.role_name
            FROM users u
            JOIN user_roles ur ON u.id = ur.user_id
            JOIN roles r ON ur.role_id = r.id
            WHERE u.username = ? AND u.is_active = TRUE
        """, (username,))
        
        user = cursor.fetchone()
        conn.close()
        
        if user and bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
            return {
                'id': user['id'],
                'username': user['username'],
                'full_name': user['full_name'],
                'email': user['email'],
                'department': user['department'],
                'role': user['role_name']
            }
        return None
    
    def get_tasks_summary(self):
        """Get tasks summary for dashboard"""
        conn = self.get_connection()
        df = pd.read_sql_query("""
            SELECT task_number, title, deadline, status, estimated_hours, budget_allocation
            FROM tasks
            ORDER BY task_number
        """, conn)
        conn.close()
        return df
    
    def get_labs_summary(self):
        """Get laboratories summary"""
        conn = self.get_connection()
        df = pd.read_sql_query("""
            SELECT lab_name, lab_code, location, capacity, status, last_inspection
            FROM laboratories
            ORDER BY lab_name
        """, conn)
        conn.close()
        return df
    
    def update_task_status(self, task_id, status):
        """Update task status"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE tasks SET status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """, (status, task_id))
        conn.commit()
        conn.close()
    
    def get_kpi_data(self):
        """Get KPI data for dashboard"""
        conn = self.get_connection()

        # Task completion stats
        cursor = conn.cursor()
        cursor.execute("""
            SELECT status, COUNT(*) as count
            FROM tasks
            GROUP BY status
        """)
        task_stats = dict(cursor.fetchall())

        # Lab operational stats
        cursor.execute("""
            SELECT status, COUNT(*) as count
            FROM laboratories
            GROUP BY status
        """)
        lab_stats = dict(cursor.fetchall())

        # Budget utilization
        cursor.execute("""
            SELECT SUM(budget_allocation) as total_budget,
                   SUM(CASE WHEN status = 'Completed' THEN budget_allocation ELSE 0 END) as used_budget
            FROM tasks
        """)
        budget_row = cursor.fetchone()
        budget_data = {
            'total_budget': budget_row['total_budget'] if budget_row['total_budget'] else 0,
            'used_budget': budget_row['used_budget'] if budget_row['used_budget'] else 0
        }

        conn.close()

        return {
            'task_stats': task_stats,
            'lab_stats': lab_stats,
            'budget_data': budget_data
        }

    def get_suppliers_summary(self):
        """Get suppliers summary"""
        conn = self.get_connection()
        df = pd.read_sql_query("""
            SELECT supplier_name, contact_person, email, phone, specialization, rating, is_active
            FROM suppliers
            ORDER BY supplier_name
        """, conn)
        conn.close()
        return df

    def get_maintenance_requests(self):
        """Get maintenance requests"""
        conn = self.get_connection()
        df = pd.read_sql_query("""
            SELECT mr.id, l.lab_name, e.equipment_name, mr.request_type,
                   mr.description, mr.priority, mr.status, mr.requested_at,
                   u.full_name as requested_by_name
            FROM maintenance_requests mr
            JOIN equipment e ON mr.equipment_id = e.id
            JOIN laboratories l ON e.lab_id = l.id
            JOIN users u ON mr.requested_by = u.id
            ORDER BY mr.requested_at DESC
        """, conn)
        conn.close()
        return df

    def add_maintenance_request(self, equipment_id, request_type, description, priority, requested_by):
        """Add new maintenance request"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO maintenance_requests (equipment_id, request_type, description, priority, requested_by)
            VALUES (?, ?, ?, ?, ?)
        """, (equipment_id, request_type, description, priority, requested_by))
        conn.commit()
        conn.close()

    def get_equipment_by_lab(self, lab_id=None):
        """Get equipment list, optionally filtered by lab"""
        conn = self.get_connection()
        if lab_id:
            df = pd.read_sql_query("""
                SELECT e.id, e.equipment_name, e.model, e.serial_number,
                       e.manufacturer, e.status, l.lab_name
                FROM equipment e
                JOIN laboratories l ON e.lab_id = l.id
                WHERE e.lab_id = ?
                ORDER BY e.equipment_name
            """, conn, params=(lab_id,))
        else:
            df = pd.read_sql_query("""
                SELECT e.id, e.equipment_name, e.model, e.serial_number,
                       e.manufacturer, e.status, l.lab_name
                FROM equipment e
                JOIN laboratories l ON e.lab_id = l.id
                ORDER BY l.lab_name, e.equipment_name
            """, conn)
        conn.close()
        return df
