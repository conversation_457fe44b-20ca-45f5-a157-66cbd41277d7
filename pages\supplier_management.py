import streamlit as st
import pandas as pd
import plotly.express as px

def show_supplier_management():
    """Supplier management page"""
    st.title("🚚 Supplier Management")
    
    db_manager = st.session_state.db_manager
    suppliers_df = db_manager.get_suppliers_summary()
    
    # Supplier overview metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Suppliers", len(suppliers_df))
    
    with col2:
        active_suppliers = len(suppliers_df[suppliers_df['is_active'] == True])
        st.metric("Active Suppliers", active_suppliers)
    
    with col3:
        avg_rating = suppliers_df['rating'].mean() if not suppliers_df.empty else 0
        st.metric("Average Rating", f"{avg_rating:.1f}/5.0")
    
    with col4:
        specializations = suppliers_df['specialization'].nunique() if not suppliers_df.empty else 0
        st.metric("Specializations", specializations)
    
    st.divider()
    
    # Tabs for different views
    tab1, tab2, tab3 = st.tabs(["📋 Supplier Directory", "➕ Add Supplier", "📊 Analytics"])
    
    with tab1:
        show_supplier_directory(suppliers_df)
    
    with tab2:
        show_add_supplier_form()
    
    with tab3:
        show_supplier_analytics(suppliers_df)

def show_supplier_directory(suppliers_df):
    """Supplier directory tab"""
    st.subheader("📋 Supplier Directory")
    
    if suppliers_df.empty:
        st.info("No suppliers found. Add suppliers using the 'Add Supplier' tab.")
        return
    
    # Search and filter options
    col1, col2, col3 = st.columns(3)
    
    with col1:
        search_term = st.text_input("🔍 Search suppliers", placeholder="Enter supplier name...")
    
    with col2:
        specialization_filter = st.selectbox(
            "Filter by Specialization",
            ["All"] + list(suppliers_df['specialization'].unique())
        )
    
    with col3:
        status_filter = st.selectbox(
            "Filter by Status",
            ["All", "Active", "Inactive"]
        )
    
    # Apply filters
    filtered_suppliers = suppliers_df.copy()
    
    if search_term:
        filtered_suppliers = filtered_suppliers[
            filtered_suppliers['supplier_name'].str.contains(search_term, case=False) |
            filtered_suppliers['contact_person'].str.contains(search_term, case=False, na=False)
        ]
    
    if specialization_filter != "All":
        filtered_suppliers = filtered_suppliers[
            filtered_suppliers['specialization'] == specialization_filter
        ]
    
    if status_filter == "Active":
        filtered_suppliers = filtered_suppliers[filtered_suppliers['is_active'] == True]
    elif status_filter == "Inactive":
        filtered_suppliers = filtered_suppliers[filtered_suppliers['is_active'] == False]
    
    # Display suppliers
    if not filtered_suppliers.empty:
        for _, supplier in filtered_suppliers.iterrows():
            with st.expander(f"{supplier['supplier_name']} - {supplier['specialization']}", expanded=False):
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.write(f"**Contact Person:** {supplier['contact_person']}")
                    st.write(f"**Email:** {supplier['email']}")
                    st.write(f"**Phone:** {supplier['phone']}")
                
                with col2:
                    st.write(f"**Specialization:** {supplier['specialization']}")
                    
                    # Rating display with stars
                    rating = supplier['rating']
                    stars = "⭐" * int(rating) + "☆" * (5 - int(rating))
                    st.write(f"**Rating:** {stars} ({rating}/5.0)")
                
                with col3:
                    status_icon = "🟢" if supplier['is_active'] else "🔴"
                    status_text = "Active" if supplier['is_active'] else "Inactive"
                    st.write(f"**Status:** {status_icon} {status_text}")
                
                # Action buttons
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    if st.button(f"Edit", key=f"edit_{supplier['supplier_name']}"):
                        st.session_state.edit_supplier = supplier['supplier_name']
                
                with col2:
                    if st.button(f"Contact", key=f"contact_{supplier['supplier_name']}"):
                        st.info(f"Contact functionality for {supplier['supplier_name']} would be implemented here.")
                
                with col3:
                    action_text = "Deactivate" if supplier['is_active'] else "Activate"
                    if st.button(action_text, key=f"toggle_{supplier['supplier_name']}"):
                        st.success(f"Supplier {supplier['supplier_name']} {action_text.lower()}d successfully!")
    else:
        st.info("No suppliers match the current filters.")

def show_add_supplier_form():
    """Add new supplier form"""
    st.subheader("➕ Add New Supplier")
    
    with st.form("add_supplier_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            supplier_name = st.text_input("Supplier Name *", placeholder="Enter supplier company name")
            contact_person = st.text_input("Contact Person", placeholder="Enter contact person name")
            email = st.text_input("Email", placeholder="Enter email address")
            phone = st.text_input("Phone", placeholder="Enter phone number")
        
        with col2:
            specialization = st.selectbox(
                "Specialization *",
                [
                    "Medical Imaging Equipment",
                    "Diagnostic Equipment", 
                    "Healthcare Technology",
                    "Laboratory Supplies",
                    "Maintenance Services",
                    "Software Solutions",
                    "Other"
                ]
            )
            
            rating = st.slider("Initial Rating", 1.0, 5.0, 3.0, 0.1)
            
            is_active = st.checkbox("Active Supplier", value=True)
        
        address = st.text_area("Address", placeholder="Enter supplier address")
        notes = st.text_area("Notes", placeholder="Additional notes about the supplier")
        
        submitted = st.form_submit_button("Add Supplier")
        
        if submitted:
            if supplier_name and specialization:
                # Here you would add the supplier to the database
                st.success(f"Supplier '{supplier_name}' added successfully!")
                st.balloons()
            else:
                st.error("Please fill in all required fields (marked with *)")

def show_supplier_analytics(suppliers_df):
    """Supplier analytics tab"""
    st.subheader("📊 Supplier Analytics")
    
    if suppliers_df.empty:
        st.info("No supplier data available for analysis.")
        return
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Suppliers by specialization
        st.subheader("Suppliers by Specialization")
        specialization_counts = suppliers_df['specialization'].value_counts()
        
        fig = px.pie(
            values=specialization_counts.values,
            names=specialization_counts.index,
            title="Supplier Distribution by Specialization"
        )
        fig.update_traces(textposition='inside', textinfo='percent+label')
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # Rating distribution
        st.subheader("Supplier Ratings")
        
        # Create rating bins
        suppliers_df['rating_bin'] = pd.cut(
            suppliers_df['rating'], 
            bins=[0, 2, 3, 4, 5], 
            labels=['1-2 Stars', '2-3 Stars', '3-4 Stars', '4-5 Stars']
        )
        
        rating_counts = suppliers_df['rating_bin'].value_counts()
        
        fig = px.bar(
            x=rating_counts.index,
            y=rating_counts.values,
            title="Supplier Rating Distribution",
            labels={'x': 'Rating Range', 'y': 'Number of Suppliers'},
            color=rating_counts.values,
            color_continuous_scale='RdYlGn'
        )
        st.plotly_chart(fig, use_container_width=True)
    
    # Top rated suppliers
    st.subheader("🏆 Top Rated Suppliers")
    top_suppliers = suppliers_df.nlargest(5, 'rating')[['supplier_name', 'specialization', 'rating', 'contact_person']]
    
    for i, (_, supplier) in enumerate(top_suppliers.iterrows(), 1):
        col1, col2, col3, col4 = st.columns([1, 3, 2, 1])
        
        with col1:
            medal = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣"][i-1]
            st.write(medal)
        
        with col2:
            st.write(f"**{supplier['supplier_name']}**")
        
        with col3:
            st.write(supplier['specialization'])
        
        with col4:
            stars = "⭐" * int(supplier['rating'])
            st.write(f"{stars} {supplier['rating']}")
    
    st.divider()
    
    # Supplier performance metrics
    st.subheader("📈 Performance Metrics")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Average rating by specialization
        avg_rating_by_spec = suppliers_df.groupby('specialization')['rating'].mean().sort_values(ascending=False)
        
        st.write("**Average Rating by Specialization:**")
        for spec, rating in avg_rating_by_spec.items():
            st.write(f"• {spec}: {rating:.1f}/5.0")
    
    with col2:
        # Active vs Inactive suppliers
        active_count = len(suppliers_df[suppliers_df['is_active'] == True])
        inactive_count = len(suppliers_df[suppliers_df['is_active'] == False])
        
        st.write("**Supplier Status:**")
        st.write(f"• Active: {active_count}")
        st.write(f"• Inactive: {inactive_count}")
        st.write(f"• Active Rate: {(active_count/(active_count+inactive_count)*100):.1f}%")
    
    with col3:
        # Contact information completeness
        complete_contacts = len(suppliers_df.dropna(subset=['contact_person', 'email', 'phone']))
        total_suppliers = len(suppliers_df)
        
        st.write("**Data Completeness:**")
        st.write(f"• Complete Contacts: {complete_contacts}/{total_suppliers}")
        st.write(f"• Completeness Rate: {(complete_contacts/total_suppliers*100):.1f}%")
    
    # Supplier contact summary table
    st.subheader("📞 Quick Contact Reference")
    contact_summary = suppliers_df[suppliers_df['is_active'] == True][
        ['supplier_name', 'contact_person', 'email', 'phone', 'specialization']
    ].sort_values('supplier_name')
    
    st.dataframe(contact_summary, use_container_width=True, hide_index=True)
