# نظام إدارة لجنة معدات المختبرات
# Laboratory Equipment Committee Management System

تطبيق ويب شامل مبني بـ Streamlit لإدارة سير عمل لجنة معدات المختبرات في قسم الأشعة التشخيصية، كلية العلوم الطبية التطبيقية.

A comprehensive full-stack web application built with Streamlit for managing the Laboratory Equipment Committee workflow of the Department of Diagnostic Radiology, College of Applied Medical Sciences.

## 🏥 نظرة عامة | Overview

يطبق هذا النظام جدولة سير العمل الكاملة للجنة معدات المختبرات، ويدير 14 مهمة مجدولة من سبتمبر 2025 إلى أبريل 2026، مع لوحات تحكم متكاملة وقدرات إدارة قواعد البيانات.

This system implements the complete workflow schedule for the Laboratory Equipment Committee, managing 14 scheduled tasks from September 2025 to April 2026, with integrated dashboards and database management capabilities.

## ✨ الميزات | Features

### 🎯 الوظائف الأساسية | Core Functionality
- **إدارة المهام | Task Management**: تتبع سير العمل الكامل لجميع المهام الـ 14 المجدولة
- **إدارة المختبرات | Laboratory Management**: جرد المعدات والصيانة ومراقبة الحالة
- **إدارة الموردين | Supplier Management**: قاعدة بيانات الموردين مع التقييمات وإدارة جهات الاتصال
- **السلامة والامتثال | Safety & Compliance**: تتبع التدقيق ومراقبة الامتثال التنظيمي
- **التكامل الأكاديمي | Academic Integration**: تقييم تأثير المقررات والحلول البديلة
- **المشتريات | Procurement**: إدارة الطلبات (تم إزالة إدارة الميزانية)
- **التقارير والتحليلات | Reports & Analytics**: لوحات تحكم KPI شاملة ومقاييس الأداء

### 🔐 الأمان والتحكم في الوصول | Security & Access Control
- نظام مصادقة قائم على الأدوار | Role-based authentication system
- أدوار المستخدمين: رئيس اللجنة، عضو اللجنة، مشرف المختبر، عضو هيئة التدريس، مدير
- User roles: Committee Chair, Committee Member, Lab Supervisor, Faculty, Admin
- تشفير آمن لكلمات المرور باستخدام bcrypt | Secure password hashing with bcrypt
- إدارة الجلسات ومسارات التدقيق | Session management and audit trails

### 📊 ميزات لوحة التحكم | Dashboard Features
- مراقبة KPI في الوقت الفعلي | Real-time KPI monitoring
- مخططات وتصورات تفاعلية | Interactive charts and visualizations
- تتبع تقدم المهام | Task progress tracking
- حالة تشغيل المختبرات | Laboratory operational status
- إدارة طلبات الصيانة | Maintenance request management
- **تم إزالة تحليل استخدام الميزانية | Budget utilization analysis removed**

## 🚀 البدء السريع | Quick Start

### المتطلبات المسبقة | Prerequisites
- Python 3.8 أو أحدث | Python 3.8 or higher
- مدير الحزم pip | pip package manager

### التثبيت والإعداد | Installation & Setup

1. **استنساخ أو تنزيل ملفات المشروع | Clone or download the project files**
2. **تشغيل سكريبت البدء | Run the startup script:**
   ```bash
   python run_app.py
   ```

سيقوم سكريبت البدء بـ | The startup script will:
- تثبيت جميع التبعيات المطلوبة | Install all required dependencies
- إنشاء الدلائل الضرورية | Create necessary directories
- تهيئة قاعدة بيانات SQLite | Initialize the SQLite database
- تشغيل تطبيق Streamlit | Launch the Streamlit application

3. **الوصول إلى التطبيق | Access the application:**
   - افتح المتصفح على `http://localhost:8501` | Open your browser to `http://localhost:8501`
   - استخدم بيانات الاعتماد النموذجية المقدمة أدناه | Use the sample credentials provided below

## 👥 بيانات تسجيل الدخول النموذجية | Sample Login Credentials

| الدور | Role | اسم المستخدم | Username | كلمة المرور | Password | مستوى الوصول | Access Level |
|------|------|------------|----------|------------|----------|-------------|--------------|
| رئيس اللجنة | Committee Chair | `chair` | `chair` | `chair` | `chair` | وصول كامل للنظام | Full system access |
| عضو اللجنة | Committee Member | `member1` | `member1` | `member1` | `member1` | وصول للمهام واللجنة | Task and committee access |
| مشرف المختبر | Lab Supervisor | `supervisor1` | `supervisor1` | `supervisor1` | `supervisor1` | وصول للمعدات والصيانة | Equipment and maintenance access |
| عضو هيئة التدريس | Faculty | `faculty1` | `faculty1` | `faculty1` | `faculty1` | وصول للتكامل الأكاديمي | Academic integration access |
| المدير | Admin | `admin` | `admin` | `admin` | `admin` | إدارة النظام | System administration |

## 📋 Workflow Tasks

The system implements all 14 scheduled tasks:

1. **Establish and update skills lab database** (Due: 2025-09-30)
2. **Establish/update supplier database** (Due: 2025-10-10)
3. **Assess lab needs & maintenance** (Due: 2025-10-20)
4. **Propose lab development/upgrade plans** (Due: 2025-11-05)
5. **Academic integration & alternatives** (Due: 2025-11-20)
6. **Update lab policies, SOPs, and manuals** (Due: 2025-12-10)
7. **Monitor health & safety compliance** (Due: 2025-12-20)
8. **Organize training & support** (Due: 2026-01-10)
9. **Manage procurement & supply** (Due: 2026-01-25)
10. **Oversee maintenance execution** (Due: 2026-02-10)
11. **Review lab utilization & efficiency** (Due: 2026-02-28)
12. **Prepare and submit annual report** (Due: 2026-03-31)
13. **Submit meeting minutes** (Due: 2026-04-30)
14. **Stakeholder feedback & policy improvement** (Due: 2026-04-15)

## 🗄️ Database Structure

The system uses SQLite with 23 interconnected tables:

### Core Tables
- **Users & Authentication**: users, roles, user_roles
- **Task Management**: tasks, task_activities, task_assignments, deliverables
- **Laboratory Management**: laboratories, equipment, maintenance_requests
- **Supplier Management**: suppliers, procurement_orders
- **Safety & Compliance**: safety_audits, compliance_records
- **Academic Integration**: courses, course_lab_dependencies
- **Meeting Management**: meetings

## 🎨 User Interface

### Navigation Structure
- **Dashboard**: Overview with KPIs and recent activities
- **Task Management**: Detailed task tracking and progress updates
- **Laboratory Management**: Equipment inventory and maintenance
- **Supplier Management**: Vendor database and contact management
- **Safety & Compliance**: Audit scheduling and compliance tracking
- **Academic Integration**: Course impact and alternative solutions
- **Procurement**: Order management and budget tracking
- **Reports**: Analytics and performance metrics
- **Settings**: System configuration

### Key Dashboard Components
- **KPI Cards**: Task completion, lab operational status, budget utilization
- **Progress Charts**: Task status distribution, timeline visualization
- **Recent Activities**: System activity feed
- **Upcoming Deadlines**: Critical task reminders
- **Laboratory Status**: Real-time equipment and facility status

## 🔧 Technical Architecture

### Backend
- **Database**: SQLite with normalized schema
- **Authentication**: bcrypt password hashing
- **Data Layer**: Pandas for data manipulation
- **File Management**: Document upload and version control

### Frontend
- **Framework**: Streamlit with custom CSS styling
- **Visualizations**: Plotly for interactive charts
- **UI Components**: Streamlit-option-menu for navigation
- **Responsive Design**: Mobile and desktop compatibility

### Key Dependencies
- `streamlit`: Web application framework
- `pandas`: Data manipulation and analysis
- `plotly`: Interactive visualizations
- `bcrypt`: Password hashing
- `sqlite3`: Database management

## 📈 Performance Metrics

The system tracks multiple KPIs:
- **Task Completion Rate**: Progress against scheduled milestones
- **Laboratory Operational Status**: Equipment availability and maintenance
- **Budget Utilization**: Spending tracking and variance analysis
- **Safety Compliance**: Audit completion and regulatory adherence
- **Academic Impact**: Course disruption and mitigation effectiveness

## 🛠️ Customization

### Adding New Users
1. Access the Admin panel
2. Navigate to User Management
3. Create new user accounts with appropriate roles

### Modifying Tasks
1. Update task definitions in the database
2. Modify activities and deliverables as needed
3. Adjust deadlines and budget allocations

### Extending Functionality
- Add new dashboard widgets in `components/dashboard_components.py`
- Create additional pages in the `pages/` directory
- Extend database schema in `database/schema.sql`

## 🔒 Security Considerations

- All passwords are hashed using bcrypt
- Role-based access control prevents unauthorized access
- Session management with secure authentication
- Audit trails for all system activities
- File upload validation and security

## 📞 Support

For technical support or questions about the Laboratory Equipment Committee Management System:

- **Department**: Diagnostic Radiology
- **Institution**: College of Applied Medical Sciences
- **System**: Committee Management Platform

## 📄 License

This system is developed for the Laboratory Equipment Committee of the Department of Diagnostic Radiology, College of Applied Medical Sciences.

---

**🏥 Laboratory Equipment Committee Management System**  
*Streamlining committee operations through digital excellence*
