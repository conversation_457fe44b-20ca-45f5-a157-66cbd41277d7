# Laboratory Equipment Committee Management System

A comprehensive full-stack web application built with Streamlit for managing the Laboratory Equipment Committee workflow of the Department of Diagnostic Radiology, College of Applied Medical Sciences.

## 🏥 Overview

This system implements the complete workflow schedule for the Laboratory Equipment Committee, managing 14 scheduled tasks from September 2025 to April 2026, with integrated dashboards and database management capabilities.

## ✨ Features

### 🎯 Core Functionality
- **Task Management**: Complete workflow tracking for all 14 scheduled tasks
- **Laboratory Management**: Equipment inventory, maintenance, and status monitoring
- **Supplier Management**: Vendor database with ratings and contact management
- **Safety & Compliance**: Audit tracking and regulatory compliance monitoring
- **Academic Integration**: Course impact assessment and alternative solutions
- **Procurement**: Order management and budget tracking
- **Reports & Analytics**: Comprehensive KPI dashboards and performance metrics

### 🔐 Security & Access Control
- Role-based authentication system
- User roles: Committee Chair, Committee Member, Lab Supervisor, Faculty, Admin
- Secure password hashing with bcrypt
- Session management and audit trails

### 📊 Dashboard Features
- Real-time KPI monitoring
- Interactive charts and visualizations
- Task progress tracking
- Budget utilization analysis
- Laboratory operational status
- Maintenance request management

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Installation & Setup

1. **Clone or download the project files**
2. **Run the startup script:**
   ```bash
   python run_app.py
   ```

The startup script will:
- Install all required dependencies
- Create necessary directories
- Initialize the SQLite database
- Launch the Streamlit application

3. **Access the application:**
   - Open your browser to `http://localhost:8501`
   - Use the sample credentials provided below

## 👥 Sample Login Credentials

| Role | Username | Password | Access Level |
|------|----------|----------|--------------|
| Committee Chair | `chair` | `chair` | Full system access |
| Committee Member | `member1` | `member1` | Task and committee access |
| Lab Supervisor | `supervisor1` | `supervisor1` | Equipment and maintenance access |
| Faculty | `faculty1` | `faculty1` | Academic integration access |
| Admin | `admin` | `admin` | System administration |

## 📋 Workflow Tasks

The system implements all 14 scheduled tasks:

1. **Establish and update skills lab database** (Due: 2025-09-30)
2. **Establish/update supplier database** (Due: 2025-10-10)
3. **Assess lab needs & maintenance** (Due: 2025-10-20)
4. **Propose lab development/upgrade plans** (Due: 2025-11-05)
5. **Academic integration & alternatives** (Due: 2025-11-20)
6. **Update lab policies, SOPs, and manuals** (Due: 2025-12-10)
7. **Monitor health & safety compliance** (Due: 2025-12-20)
8. **Organize training & support** (Due: 2026-01-10)
9. **Manage procurement & supply** (Due: 2026-01-25)
10. **Oversee maintenance execution** (Due: 2026-02-10)
11. **Review lab utilization & efficiency** (Due: 2026-02-28)
12. **Prepare and submit annual report** (Due: 2026-03-31)
13. **Submit meeting minutes** (Due: 2026-04-30)
14. **Stakeholder feedback & policy improvement** (Due: 2026-04-15)

## 🗄️ Database Structure

The system uses SQLite with 23 interconnected tables:

### Core Tables
- **Users & Authentication**: users, roles, user_roles
- **Task Management**: tasks, task_activities, task_assignments, deliverables
- **Laboratory Management**: laboratories, equipment, maintenance_requests
- **Supplier Management**: suppliers, procurement_orders
- **Safety & Compliance**: safety_audits, compliance_records
- **Academic Integration**: courses, course_lab_dependencies
- **Meeting Management**: meetings

## 🎨 User Interface

### Navigation Structure
- **Dashboard**: Overview with KPIs and recent activities
- **Task Management**: Detailed task tracking and progress updates
- **Laboratory Management**: Equipment inventory and maintenance
- **Supplier Management**: Vendor database and contact management
- **Safety & Compliance**: Audit scheduling and compliance tracking
- **Academic Integration**: Course impact and alternative solutions
- **Procurement**: Order management and budget tracking
- **Reports**: Analytics and performance metrics
- **Settings**: System configuration

### Key Dashboard Components
- **KPI Cards**: Task completion, lab operational status, budget utilization
- **Progress Charts**: Task status distribution, timeline visualization
- **Recent Activities**: System activity feed
- **Upcoming Deadlines**: Critical task reminders
- **Laboratory Status**: Real-time equipment and facility status

## 🔧 Technical Architecture

### Backend
- **Database**: SQLite with normalized schema
- **Authentication**: bcrypt password hashing
- **Data Layer**: Pandas for data manipulation
- **File Management**: Document upload and version control

### Frontend
- **Framework**: Streamlit with custom CSS styling
- **Visualizations**: Plotly for interactive charts
- **UI Components**: Streamlit-option-menu for navigation
- **Responsive Design**: Mobile and desktop compatibility

### Key Dependencies
- `streamlit`: Web application framework
- `pandas`: Data manipulation and analysis
- `plotly`: Interactive visualizations
- `bcrypt`: Password hashing
- `sqlite3`: Database management

## 📈 Performance Metrics

The system tracks multiple KPIs:
- **Task Completion Rate**: Progress against scheduled milestones
- **Laboratory Operational Status**: Equipment availability and maintenance
- **Budget Utilization**: Spending tracking and variance analysis
- **Safety Compliance**: Audit completion and regulatory adherence
- **Academic Impact**: Course disruption and mitigation effectiveness

## 🛠️ Customization

### Adding New Users
1. Access the Admin panel
2. Navigate to User Management
3. Create new user accounts with appropriate roles

### Modifying Tasks
1. Update task definitions in the database
2. Modify activities and deliverables as needed
3. Adjust deadlines and budget allocations

### Extending Functionality
- Add new dashboard widgets in `components/dashboard_components.py`
- Create additional pages in the `pages/` directory
- Extend database schema in `database/schema.sql`

## 🔒 Security Considerations

- All passwords are hashed using bcrypt
- Role-based access control prevents unauthorized access
- Session management with secure authentication
- Audit trails for all system activities
- File upload validation and security

## 📞 Support

For technical support or questions about the Laboratory Equipment Committee Management System:

- **Department**: Diagnostic Radiology
- **Institution**: College of Applied Medical Sciences
- **System**: Committee Management Platform

## 📄 License

This system is developed for the Laboratory Equipment Committee of the Department of Diagnostic Radiology, College of Applied Medical Sciences.

---

**🏥 Laboratory Equipment Committee Management System**  
*Streamlining committee operations through digital excellence*
