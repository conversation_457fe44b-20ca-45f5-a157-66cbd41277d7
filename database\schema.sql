-- Laboratory Equipment Committee Management System Database Schema

-- Users and Authentication
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    department VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

CREATE TABLE IF NOT EXISTS roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT
);

CREATE TABLE IF NOT EXISTS user_roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    role_id INTEGER NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    <PERSON>OREI<PERSON><PERSON> KEY (user_id) REFERENCES users(id),
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (role_id) REFERENCES roles(id)
);

-- Task Management
CREATE TABLE IF NOT EXISTS tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_number INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    deadline DATE NOT NULL,
    estimated_hours INTEGER,
    budget_allocation DECIMAL(10,2),
    status VARCHAR(20) DEFAULT 'Not Started',
    priority VARCHAR(10) DEFAULT 'Medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS task_activities (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    activity_description TEXT NOT NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id)
);

CREATE TABLE IF NOT EXISTS task_assignments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    role_in_task VARCHAR(50),
    FOREIGN KEY (task_id) REFERENCES tasks(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS deliverables (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    deliverable_name VARCHAR(200) NOT NULL,
    description TEXT,
    file_path VARCHAR(500),
    status VARCHAR(20) DEFAULT 'Pending',
    submitted_at TIMESTAMP,
    approved_at TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id)
);

-- Laboratory and Equipment Management
CREATE TABLE IF NOT EXISTS laboratories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    lab_name VARCHAR(100) NOT NULL,
    lab_code VARCHAR(20) UNIQUE NOT NULL,
    location VARCHAR(100),
    capacity INTEGER,
    status VARCHAR(20) DEFAULT 'Operational',
    last_inspection DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS equipment (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    lab_id INTEGER NOT NULL,
    equipment_name VARCHAR(100) NOT NULL,
    model VARCHAR(100),
    serial_number VARCHAR(100),
    manufacturer VARCHAR(100),
    purchase_date DATE,
    warranty_expiry DATE,
    status VARCHAR(20) DEFAULT 'Operational',
    last_maintenance DATE,
    next_maintenance DATE,
    FOREIGN KEY (lab_id) REFERENCES laboratories(id)
);

CREATE TABLE IF NOT EXISTS maintenance_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    equipment_id INTEGER NOT NULL,
    request_type VARCHAR(50) NOT NULL,
    description TEXT,
    priority VARCHAR(10) DEFAULT 'Medium',
    status VARCHAR(20) DEFAULT 'Open',
    requested_by INTEGER NOT NULL,
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    cost DECIMAL(10,2),
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    FOREIGN KEY (requested_by) REFERENCES users(id)
);

-- Supplier Management
CREATE TABLE IF NOT EXISTS suppliers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supplier_name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    specialization TEXT,
    rating DECIMAL(3,2),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Procurement Management
CREATE TABLE IF NOT EXISTS procurement_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id INTEGER NOT NULL,
    order_date DATE NOT NULL,
    expected_delivery DATE,
    total_amount DECIMAL(12,2),
    status VARCHAR(20) DEFAULT 'Pending',
    created_by INTEGER NOT NULL,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Safety and Compliance
CREATE TABLE IF NOT EXISTS safety_audits (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    lab_id INTEGER NOT NULL,
    audit_date DATE NOT NULL,
    auditor_id INTEGER NOT NULL,
    audit_type VARCHAR(50),
    findings TEXT,
    recommendations TEXT,
    compliance_score DECIMAL(5,2),
    status VARCHAR(20) DEFAULT 'Scheduled',
    FOREIGN KEY (lab_id) REFERENCES laboratories(id),
    FOREIGN KEY (auditor_id) REFERENCES users(id)
);

-- Academic Integration
CREATE TABLE IF NOT EXISTS courses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    course_code VARCHAR(20) UNIQUE NOT NULL,
    course_name VARCHAR(100) NOT NULL,
    department VARCHAR(100),
    semester VARCHAR(20),
    instructor_id INTEGER,
    FOREIGN KEY (instructor_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS course_lab_dependencies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    course_id INTEGER NOT NULL,
    lab_id INTEGER NOT NULL,
    equipment_id INTEGER,
    dependency_type VARCHAR(50),
    FOREIGN KEY (course_id) REFERENCES courses(id),
    FOREIGN KEY (lab_id) REFERENCES laboratories(id),
    FOREIGN KEY (equipment_id) REFERENCES equipment(id)
);

-- Meeting Management
CREATE TABLE IF NOT EXISTS meetings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    meeting_date DATE NOT NULL,
    meeting_type VARCHAR(50),
    agenda TEXT,
    minutes TEXT,
    attendees TEXT,
    action_items TEXT,
    created_by INTEGER NOT NULL,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Insert default roles
INSERT OR IGNORE INTO roles (role_name, description) VALUES 
('Committee Chair', 'Committee chairperson with full access'),
('Committee Member', 'Committee member with task access'),
('Lab Supervisor', 'Laboratory supervisor with equipment access'),
('Faculty', 'Faculty member with course integration access'),
('Admin', 'System administrator');
