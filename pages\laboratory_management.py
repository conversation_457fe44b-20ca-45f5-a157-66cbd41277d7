import streamlit as st
import pandas as pd
import plotly.express as px
from datetime import datetime

def show_laboratory_management():
    """Enhanced laboratory management page"""
    st.title("🔬 Laboratory Management")
    
    db_manager = st.session_state.db_manager
    labs_df = db_manager.get_labs_summary()
    equipment_df = db_manager.get_equipment_by_lab()
    maintenance_df = db_manager.get_maintenance_requests()
    
    # Lab overview metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Labs", len(labs_df))
    
    with col2:
        operational = len(labs_df[labs_df['status'] == 'Operational'])
        operational_rate = (operational / len(labs_df) * 100) if len(labs_df) > 0 else 0
        st.metric("Operational", operational, f"{operational_rate:.1f}%")
    
    with col3:
        maintenance = len(labs_df[labs_df['status'] == 'Under Maintenance'])
        st.metric("Under Maintenance", maintenance)
    
    with col4:
        total_capacity = labs_df['capacity'].sum()
        st.metric("Total Capacity", total_capacity)
    
    st.divider()
    
    # Tabs for different views
    tab1, tab2, tab3, tab4 = st.tabs(["🏢 Laboratories", "⚙️ Equipment", "🔧 Maintenance", "📊 Analytics"])
    
    with tab1:
        show_laboratories_tab(labs_df)
    
    with tab2:
        show_equipment_tab(equipment_df, labs_df)
    
    with tab3:
        show_maintenance_tab(maintenance_df, equipment_df)
    
    with tab4:
        show_analytics_tab(labs_df, equipment_df, maintenance_df)

def show_laboratories_tab(labs_df):
    """Laboratory overview tab"""
    st.subheader("🏢 Laboratory Overview")
    
    # Lab status visualization
    col1, col2 = st.columns(2)
    
    with col1:
        # Status distribution pie chart
        if not labs_df.empty:
            status_counts = labs_df['status'].value_counts()
            fig = px.pie(
                values=status_counts.values,
                names=status_counts.index,
                title="Laboratory Status Distribution",
                color_discrete_map={
                    'Operational': '#90ee90',
                    'Under Maintenance': '#ffb347',
                    'Out of Order': '#ff7f7f'
                }
            )
            st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # Capacity by lab
        if not labs_df.empty:
            fig = px.bar(
                labs_df,
                x='lab_name',
                y='capacity',
                color='status',
                title="Laboratory Capacity",
                labels={'capacity': 'Capacity', 'lab_name': 'Laboratory'}
            )
            fig.update_xaxis(tickangle=45)
            st.plotly_chart(fig, use_container_width=True)
    
    st.divider()
    
    # Laboratory details table
    st.subheader("📋 Laboratory Details")
    
    # Add search and filter
    col1, col2 = st.columns(2)
    with col1:
        search_term = st.text_input("🔍 Search laboratories", placeholder="Enter lab name or code...")
    with col2:
        status_filter = st.selectbox("Filter by Status", ["All"] + list(labs_df['status'].unique()))
    
    # Apply filters
    filtered_labs = labs_df.copy()
    if search_term:
        filtered_labs = filtered_labs[
            filtered_labs['lab_name'].str.contains(search_term, case=False) |
            filtered_labs['lab_code'].str.contains(search_term, case=False)
        ]
    if status_filter != "All":
        filtered_labs = filtered_labs[filtered_labs['status'] == status_filter]
    
    # Display filtered results
    if not filtered_labs.empty:
        for _, lab in filtered_labs.iterrows():
            with st.expander(f"{lab['lab_name']} ({lab['lab_code']})", expanded=False):
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.write(f"**Location:** {lab['location']}")
                    st.write(f"**Capacity:** {lab['capacity']} students")
                
                with col2:
                    status_color = {
                        'Operational': '🟢',
                        'Under Maintenance': '🟡',
                        'Out of Order': '🔴'
                    }
                    st.write(f"**Status:** {status_color.get(lab['status'], '⚪')} {lab['status']}")
                    st.write(f"**Last Inspection:** {lab['last_inspection']}")
                
                with col3:
                    if st.button(f"View Equipment", key=f"view_eq_{lab['lab_code']}"):
                        st.session_state.selected_lab = lab['lab_code']
                    
                    if st.button(f"Schedule Maintenance", key=f"maint_{lab['lab_code']}"):
                        st.session_state.schedule_maintenance = lab['lab_code']
    else:
        st.info("No laboratories match the current filters.")

def show_equipment_tab(equipment_df, labs_df):
    """Equipment management tab"""
    st.subheader("⚙️ Equipment Management")
    
    if equipment_df.empty:
        st.info("No equipment data available. Equipment will be added as part of Task 1: Establish and update skills lab database.")
        return
    
    # Equipment overview metrics
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Total Equipment", len(equipment_df))
    
    with col2:
        operational_eq = len(equipment_df[equipment_df['status'] == 'Operational'])
        st.metric("Operational", operational_eq)
    
    with col3:
        maintenance_eq = len(equipment_df[equipment_df['status'] == 'Under Maintenance'])
        st.metric("Under Maintenance", maintenance_eq)
    
    # Equipment filters
    col1, col2, col3 = st.columns(3)
    
    with col1:
        lab_filter = st.selectbox("Filter by Laboratory", ["All"] + list(labs_df['lab_name'].unique()))
    
    with col2:
        status_filter = st.selectbox("Filter by Equipment Status", ["All"] + list(equipment_df['status'].unique()))
    
    with col3:
        search_equipment = st.text_input("🔍 Search equipment", placeholder="Enter equipment name...")
    
    # Apply filters
    filtered_equipment = equipment_df.copy()
    if lab_filter != "All":
        filtered_equipment = filtered_equipment[filtered_equipment['lab_name'] == lab_filter]
    if status_filter != "All":
        filtered_equipment = filtered_equipment[filtered_equipment['status'] == status_filter]
    if search_equipment:
        filtered_equipment = filtered_equipment[
            filtered_equipment['equipment_name'].str.contains(search_equipment, case=False)
        ]
    
    # Equipment list
    st.dataframe(
        filtered_equipment[['lab_name', 'equipment_name', 'model', 'manufacturer', 'status']],
        use_container_width=True,
        hide_index=True
    )

def show_maintenance_tab(maintenance_df, equipment_df):
    """Maintenance management tab"""
    st.subheader("🔧 Maintenance Management")
    
    # Maintenance overview
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Requests", len(maintenance_df))
    
    with col2:
        open_requests = len(maintenance_df[maintenance_df['status'] == 'Open'])
        st.metric("Open Requests", open_requests)
    
    with col3:
        in_progress = len(maintenance_df[maintenance_df['status'] == 'In Progress'])
        st.metric("In Progress", in_progress)
    
    with col4:
        completed = len(maintenance_df[maintenance_df['status'] == 'Completed'])
        st.metric("Completed", completed)
    
    st.divider()
    
    # New maintenance request form
    with st.expander("➕ Submit New Maintenance Request", expanded=False):
        with st.form("maintenance_request_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                # This would be populated from equipment data
                equipment_options = ["Select Equipment"] + list(equipment_df['equipment_name'].unique()) if not equipment_df.empty else ["No equipment available"]
                selected_equipment = st.selectbox("Equipment", equipment_options)
                
                request_type = st.selectbox(
                    "Request Type",
                    ["Preventive Maintenance", "Repair", "Calibration", "Inspection", "Emergency Repair"]
                )
            
            with col2:
                priority = st.selectbox("Priority", ["Low", "Medium", "High", "Critical"])
                
            description = st.text_area("Description", placeholder="Describe the maintenance issue or requirement...")
            
            submitted = st.form_submit_button("Submit Request")
            
            if submitted and selected_equipment != "Select Equipment":
                # Add to database
                user = st.session_state.user
                # db_manager.add_maintenance_request(equipment_id, request_type, description, priority, user['id'])
                st.success("Maintenance request submitted successfully!")
    
    st.divider()
    
    # Maintenance requests list
    st.subheader("📋 Maintenance Requests")
    
    if not maintenance_df.empty:
        # Filter options
        col1, col2 = st.columns(2)
        with col1:
            status_filter = st.selectbox("Filter by Status", ["All"] + list(maintenance_df['status'].unique()), key="maint_status_filter")
        with col2:
            priority_filter = st.selectbox("Filter by Priority", ["All"] + list(maintenance_df['priority'].unique()), key="maint_priority_filter")
        
        # Apply filters
        filtered_maintenance = maintenance_df.copy()
        if status_filter != "All":
            filtered_maintenance = filtered_maintenance[filtered_maintenance['status'] == status_filter]
        if priority_filter != "All":
            filtered_maintenance = filtered_maintenance[filtered_maintenance['priority'] == priority_filter]
        
        # Display requests
        for _, request in filtered_maintenance.iterrows():
            with st.expander(f"{request['lab_name']} - {request['equipment_name']} ({request['request_type']})", expanded=False):
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.write(f"**Priority:** {request['priority']}")
                    st.write(f"**Status:** {request['status']}")
                
                with col2:
                    st.write(f"**Requested by:** {request['requested_by_name']}")
                    st.write(f"**Date:** {request['requested_at']}")
                
                with col3:
                    new_status = st.selectbox(
                        "Update Status",
                        ["Open", "In Progress", "Completed", "Cancelled"],
                        index=["Open", "In Progress", "Completed", "Cancelled"].index(request['status']),
                        key=f"maint_status_{request['id']}"
                    )
                
                st.write(f"**Description:** {request['description']}")
                
                if st.button(f"Update Request", key=f"update_maint_{request['id']}"):
                    st.success(f"Maintenance request updated to {new_status}")
    else:
        st.info("No maintenance requests found.")

def show_analytics_tab(labs_df, equipment_df, maintenance_df):
    """Analytics and reporting tab"""
    st.subheader("📊 Laboratory Analytics")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Lab utilization (placeholder - would need actual usage data)
        st.subheader("🏢 Laboratory Utilization")
        if not labs_df.empty:
            # Simulated utilization data
            utilization_data = labs_df.copy()
            utilization_data['utilization_rate'] = [85, 92, 0, 78, 88]  # Simulated data
            
            fig = px.bar(
                utilization_data,
                x='lab_name',
                y='utilization_rate',
                color='status',
                title="Laboratory Utilization Rate (%)",
                labels={'utilization_rate': 'Utilization (%)', 'lab_name': 'Laboratory'}
            )
            fig.update_xaxis(tickangle=45)
            st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # Maintenance trends
        st.subheader("🔧 Maintenance Trends")
        if not maintenance_df.empty:
            # Maintenance by priority
            priority_counts = maintenance_df['priority'].value_counts()
            fig = px.pie(
                values=priority_counts.values,
                names=priority_counts.index,
                title="Maintenance Requests by Priority",
                color_discrete_map={
                    'Critical': '#ff4444',
                    'High': '#ff8800',
                    'Medium': '#ffbb33',
                    'Low': '#00aa00'
                }
            )
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No maintenance data available for analysis.")
    
    # Equipment status overview
    st.subheader("⚙️ Equipment Status Overview")
    if not equipment_df.empty:
        equipment_status = equipment_df['status'].value_counts()
        fig = px.bar(
            x=equipment_status.index,
            y=equipment_status.values,
            title="Equipment Status Distribution",
            labels={'x': 'Status', 'y': 'Count'}
        )
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("Equipment data will be available after completing Task 1: Establish and update skills lab database.")
