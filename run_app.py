#!/usr/bin/env python3
"""
Laboratory Equipment Committee Management System
Startup script for the Streamlit application
"""

import os
import sys
import subprocess

def install_requirements():
    """Install required packages"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False
    return True

def create_directories():
    """Create necessary directories"""
    directories = [
        "database",
        "pages", 
        "components",
        "utils",
        "static",
        "uploads"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print("✅ Directories created successfully!")

def run_streamlit():
    """Run the Streamlit application"""
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Error running application: {e}")

def main():
    """Main startup function"""
    print("🏥 Laboratory Equipment Committee Management System")
    print("=" * 60)
    
    # Create directories
    create_directories()
    
    # Install requirements
    print("📦 Installing requirements...")
    if not install_requirements():
        return
    
    print("\n🚀 Starting the application...")
    print("📱 The application will open in your default browser")
    print("🔗 URL: http://localhost:8501")
    print("\n📋 Sample Login Credentials:")
    print("   Committee Chair: chair / chair")
    print("   Committee Member: member1 / member1") 
    print("   Lab Supervisor: supervisor1 / supervisor1")
    print("   Faculty: faculty1 / faculty1")
    print("   Admin: admin / admin")
    print("\n" + "=" * 60)
    
    # Run the application
    run_streamlit()

if __name__ == "__main__":
    main()
